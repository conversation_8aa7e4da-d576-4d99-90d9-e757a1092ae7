package xendit

import "time"

type Credential struct {
	Username      string `json:"username"`
	Password      string `json:"password"`
	CallbackToken string `json:"callback_token"`
}

type VaResponse struct {
	AccountNumber  string  `json:"account_number"`
	BankCode       string  `json:"bank_code"`
	Currency       string  `json:"currency"`
	ExpectedAmount float64 `json:"expected_amount"`
	ExpirationDate string  `json:"expiration_date"`
	ExternalID     string  `json:"external_id"`
	ID             string  `json:"id"`
	IsClosed       bool    `json:"is_closed"`
	IsSingleUse    bool    `json:"is_single_use"`
	MerchantCode   string  `json:"merchant_code"`
	Name           string  `json:"name"`
	OwnerID        string  `json:"owner_id"`
	Status         string  `json:"status"`
}

type CreateVARequest struct {
	ExternalId           string  `json:"external_id"`
	BankCode             string  `json:"bank_code"`
	Name                 string  `json:"name"`
	VirtualAccountNumber string  `json:"virtual_account_number,omitempty"`
	IsClosed             bool    `json:"is_closed"`
	ExpectedAmount       float64 `json:"expected_amount"`
	ExpirationDate       string  `json:"expiration_date"`
	IsSingleUse          bool    `json:"is_single_use"`
}

type UpdateVARequest struct {
	ID             string  `json:"-"`
	ExternalId     string  `json:"external_id"`
	ExpectedAmount float64 `json:"expected_amount"`
	ExpirationDate string  `json:"expiration_date"`
}

type CallbackVAResponse struct {
	AccountNumber            string  `json:"account_number"`
	BankCode                 string  `json:"bank_code"`
	Currency                 string  `json:"currency"`
	ExpirationDate           string  `json:"expiration_date"`
	ExternalID               string  `json:"external_id"`
	ID                       string  `json:"id"`
	IsClosed                 bool    `json:"is_closed"`
	IsSingleUse              bool    `json:"is_single_use"`
	MerchantCode             string  `json:"merchant_code"`
	Name                     string  `json:"name"`
	OwnerID                  string  `json:"owner_id"`
	Status                   string  `json:"status"`
	Amount                   float64 `json:"amount"`
	CallbackVirtualAccountID string  `json:"callback_virtual_account_id"`
	Created                  string  `json:"created"`
	PaymentID                string  `json:"payment_id"`
	TransactionTimestamp     string  `json:"transaction_timestamp"`
	Updated                  string  `json:"updated"`
	ExpectedAmount           float64 `json:"expected_amount,omitempty"`
}

type ErrorResponse struct {
	ErrorCode string `json:"error_code"`
	Message   string `json:"message"`
}

type CreateEwalletRequest struct {
	ReferenceId       string            `json:"reference_id"`
	Currency          string            `json:"currency"`
	Amount            float64           `json:"amount"`
	CheckoutMethod    string            `json:"checkout_method"`
	ChannelCode       string            `json:"channel_code"`
	ChannelProperties ChannelProperties `json:"channel_properties"`
}

type CreateEwalletResponse struct {
	ID                 string                `json:"id"`
	BusinessId         string                `json:"business_id"`
	ReferenceId        string                `json:"reference_id"`
	Status             string                `json:"status"`
	Currency           string                `json:"currency"`
	ChargeAmount       float64               `json:"charge_amount"`
	CheckoutMethod     string                `json:"checkout_method"`
	ChannelCode        string                `json:"channel_code"`
	IsRedirectRequired bool                  `json:"is_redirect_required"`
	CallbackUrl        string                `json:"callback_url"`
	Created            string                `json:"created"`
	Updated            string                `json:"updated"`
	CaptureNow         bool                  `json:"capture_now"`
	Actions            EwalletActionResponse `json:"actions"`
	ChannelProperties  ChannelProperties     `json:"channel_properties"`
}

type EwalletActionResponse struct {
	DesktopWebCheckoutURL     string      `json:"desktop_web_checkout_url"`
	MobileDeeplinkCheckoutURL interface{} `json:"mobile_deeplink_checkout_url"`
	MobileWebCheckoutURL      string      `json:"mobile_web_checkout_url"`
	QrCheckoutString          interface{} `json:"qr_checkout_string"`
}

type ChannelProperties struct {
	MobileNumber       string `json:"mobile_number,omitempty"`
	SuccessRedirectUrl string `json:"success_redirect_url,omitempty"`
	FailureRedirectUrl string `json:"failure_redirect_url,omitempty"`
}

type CallbackEwalletResponse struct {
	ID          string                `json:"id"`
	BusinessId  string                `json:"business_id"`
	ReferenceId string                `json:"reference_id"`
	Event       string                `json:"event"`
	Created     string                `json:"created"`
	Data        CreateEwalletResponse `json:"data"`
}

type CreateInvoiceRequest struct {
	ExternalId      string   `json:"external_id"`
	Amount          float64  `json:"amount"`
	PayerEmail      string   `json:"payer_email"`
	Description     string   `json:"description"`
	InvoiceDuration int      `json:"invoice_duration"`
	PaymentMethods  []string `json:"payment_methods"`
}

type CreateInvoiceResponse struct {
	ID                        string    `json:"id"`
	ExternalID                string    `json:"external_id"`
	UserID                    string    `json:"user_id"`
	Status                    string    `json:"status"`
	MerchantName              string    `json:"merchant_name"`
	MerchantProfilePictureURL string    `json:"merchant_profile_picture_url"`
	Amount                    int       `json:"amount"`
	PayerEmail                string    `json:"payer_email"`
	Description               string    `json:"description"`
	ExpiryDate                time.Time `json:"expiry_date"`
	InvoiceURL                string    `json:"invoice_url"`
	ShouldExcludeCreditCard   bool      `json:"should_exclude_credit_card"`
	ShouldSendEmail           bool      `json:"should_send_email"`
	Created                   time.Time `json:"created"`
	Updated                   time.Time `json:"updated"`
	Currency                  string    `json:"currency"`
}

// CallbackInvoiceResponse ...
type CallbackInvoiceResponse struct {
	ID                     string  `json:"id"`
	ExternalID             string  `json:"external_id"`
	UserID                 string  `json:"user_id"`
	IsHigh                 bool    `json:"is_high"`
	PaymentMethod          string  `json:"payment_method"`
	Status                 string  `json:"status"`
	MerchantName           string  `json:"merchant_name"`
	Amount                 float64 `json:"amount"`
	PaidAmount             float64 `json:"paid_amount"`
	BankCode               string  `json:"bank_code"`
	PaidAt                 string  `json:"paid_at"`
	PayerEmail             string  `json:"payer_email"`
	Description            string  `json:"description"`
	AdjustedReceivedAmount float64 `json:"adjusted_received_amount"`
	FeesPaidAmount         float64 `json:"fees_paid_amount"`
	Created                string  `json:"created"`
	Updated                string  `json:"updated"`
	Currency               string  `json:"currency"`
	PaymentChannel         string  `json:"payment_channel"`
	PaymentDestination     string  `json:"payment_destination"`
}

type DisbursementRequest struct {
	ExternalID        string  `json:"external_id"`
	Amount            float64 `json:"amount"`
	BankCode          string  `json:"bank_code"`
	AccountHolderName string  `json:"account_holder_name"`
	AccountNumber     string  `json:"account_number"`
	Description       string  `json:"description"`
}

type DisbursementResponse struct {
	AccountHolderName       string  `json:"account_holder_name"`
	Amount                  float64 `json:"amount"`
	BankCode                string  `json:"bank_code"`
	Created                 string  `json:"created"`
	DisbursementDescription string  `json:"disbursement_description"`
	ExternalID              string  `json:"external_id"`
	ID                      string  `json:"id"`
	IsInstant               bool    `json:"is_instant"`
	Status                  string  `json:"status"`
	Updated                 string  `json:"updated"`
	UserID                  string  `json:"user_id"`
}

type DisbursementCheckStatusResponse struct {
	AccountHolderName       string      `json:"account_holder_name"`
	Amount                  int64       `json:"amount"`
	BankCode                string      `json:"bank_code"`
	DisbursementDescription string      `json:"disbursement_description"`
	ExternalID              string      `json:"external_id"`
	ID                      string      `json:"id"`
	Status                  string      `json:"status"`
	UserID                  string      `json:"user_id"`
	IsInstant               bool        `json:"is_instant"`
	EmailTo                 interface{} `json:"email_to,omitempty"`
	EmailCc                 interface{} `json:"email_cc,omitempty"`
	EmailBcc                interface{} `json:"email_bcc,omitempty"`
}

type CreateFixedPaymentCodeRequest struct {
	ExternalID       string  `json:"external_id"`
	RetailOutletName string  `json:"retail_outlet_name"` // ALFAMART / INDOMARET
	Name             string  `json:"name"`
	ExpectedAmount   float64 `json:"expected_amount"`
	IsSingleUse      bool    `json:"is_single_use"` //value that determines whether a fixed payment code will be inactive after it is paid or not
	ExpirationDate   string  `json:"expiration_date"`
}

type FixedPaymentCodeResponse struct {
	IsSingleUse      bool      `json:"is_single_use"`
	Status           string    `json:"status"`
	OwnerID          string    `json:"owner_id"`
	ExternalID       string    `json:"external_id"`
	RetailOutletName string    `json:"retail_outlet_name"`
	Prefix           string    `json:"prefix"`
	Name             string    `json:"name"`
	PaymentCode      string    `json:"payment_code"`
	Type             string    `json:"type"`
	ExpectedAmount   float64   `json:"expected_amount"`
	ExpirationDate   time.Time `json:"expiration_date"`
	ID               string    `json:"id"`
}
type UpdateFixedPaymentCodeRequest struct {
	ID             string  `json:"-"`
	Name           string  `json:"name"`
	ExpectedAmount float64 `json:"expected_amount"`
	ExpirationDate string  `json:"expiration_date"`
}

type GetFixedPaymentCodeResponse struct {
	IsSingleUse      bool      `json:"is_single_use"`
	Status           string    `json:"status"`
	OwnerID          string    `json:"owner_id"`
	ExternalID       string    `json:"external_id"`
	RetailOutletName string    `json:"retail_outlet_name"`
	Prefix           string    `json:"prefix"`
	Name             string    `json:"name"`
	PaymentCode      string    `json:"payment_code"`
	Type             string    `json:"type"`
	ExpectedAmount   float64   `json:"expected_amount"`
	ExpirationDate   time.Time `json:"expiration_date"`
	ID               string    `json:"id"`
}

type GetFixedPaymentCodePaymentsResponse struct {
	Data    []GetFixedPaymentCodePaymentData `json:"data"`
	HasMore bool                             `json:"has_more"`
	Links   struct {
		Href   string `json:"href"`
		Rel    string `json:"rel"`
		Method string `json:"method"`
	} `json:"links"`
}

type GetFixedPaymentCodePaymentData struct {
	Status                    string    `json:"status"`
	FixedPaymentCodePaymentID string    `json:"fixed_payment_code_payment_id"`
	FixedPaymentCodeID        string    `json:"fixed_payment_code_id"`
	Amount                    int       `json:"amount"`
	Name                      string    `json:"name"`
	Prefix                    string    `json:"prefix"`
	PaymentCode               string    `json:"payment_code"`
	PaymentID                 string    `json:"payment_id"`
	ExternalID                string    `json:"external_id"`
	RetailOutletName          string    `json:"retail_outlet_name"`
	TransactionTimestamp      time.Time `json:"transaction_timestamp"`
	ID                        string    `json:"id"`
	OwnerID                   string    `json:"owner_id"`
}

type CallbackFixedPaymentCode struct {
	ID                        string    `json:"id"`
	ExternalID                string    `json:"external_id"`
	Prefix                    string    `json:"prefix"`
	PaymentCode               string    `json:"payment_code"`
	RetailOutletName          string    `json:"retail_outlet_name"`
	Name                      string    `json:"name"`
	Amount                    float64   `json:"amount"`
	Status                    string    `json:"status"`
	TransactionTimestamp      time.Time `json:"transaction_timestamp"`
	PaymentID                 string    `json:"payment_id"`
	FixedPaymentCodePaymentID string    `json:"fixed_payment_code_payment_id"`
	FixedPaymentCodeID        string    `json:"fixed_payment_code_id"`
	OwnerID                   string    `json:"owner_id"`
}
