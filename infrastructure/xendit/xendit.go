package xendit

import (
	"context"
	"encoding/base64"
	"encoding/json"
	"fmt"
	"net/http"

	"repo.nusatek.id/moaja/backend/libraries/logger"
	rest "repo.nusatek.id/moaja/backend/libraries/rest-utils"
	"repo.nusatek.id/nusatek/payment/utils/config"

	errors "repo.nusatek.id/moaja/backend/libraries/service-utils/errors"
)

const (
	ERROR_CODE_VA_NOT_FOUND = "CALLBACK_VIRTUAL_ACCOUNT_NOT_FOUND_ERROR"
	ERROR_CODE_VA_DUPLICATE = "DUPLICATE_CALLBACK_VIRTUAL_ACCOUNT_ERROR"
)

type wrapper struct {
	Endpoint  string
	isSandbox bool
	client    rest.RestClient
}

func NewWrapper() *wrapper {
	return &wrapper{}
}

func (w *wrapper) Setup() RestXendit {
	var (
		EndpointSandbox = config.GetString("xendit.sandbox")
		Endpoint        = config.GetString("xendit.host")
	)

	w.isSandbox = config.GetBool("xendit.is_sandbox")
	w.Endpoint = Endpoint
	if w.isSandbox {
		w.Endpoint = EndpointSandbox
	}

	restOptions := rest.Options{
		Address:   w.Endpoint,
		Timeout:   config.GetDuration("xendit.timeout"),
		SkipTLS:   config.GetBool("xendit.skiptls"),
		DebugMode: config.GetBool("xendit.debug"),
	}

	if config.GetString("xendit.proxy") != "" {
		restOptions.WithProxy = true
		restOptions.ProxyAddress = config.GetString("xendit.proxy")
	}

	w.client = rest.New(restOptions)

	return w
}

func setHeader(credential Credential) http.Header {
	secret := base64.StdEncoding.EncodeToString([]byte(fmt.Sprintf("%v:%v", credential.Username, credential.Password)))

	header := http.Header{}
	header.Add("Authorization", "Basic "+secret)
	header.Add("Accept", "application/json")
	header.Add("Content-Type", "application/json")

	return header
}

func (w *wrapper) CreatePaymentVA(ctx context.Context, req CreateVARequest, credential Credential) (resp VaResponse, err error) {
	path := "/callback_virtual_accounts"
	logger.Info(ctx, "request", logger.String("path", w.Endpoint+path), logger.Any("req", req))

	header := setHeader(credential)
	body, status, err := w.client.Post(ctx, path, header, req)
	if err != nil {
		err = errors.SetError(http.StatusInternalServerError, err.Error())
		logger.Error(ctx, "error request", logger.Err(err))
		return
	}
	logger.Info(ctx, "response", logger.String("resp", string(body)))

	if status != http.StatusOK {
		var datas ErrorResponse
		_ = json.Unmarshal(body, &datas)
		if datas.Message != "" {
			err = errors.SetErrorMessage(http.StatusBadRequest, datas.Message)
			logger.Error(ctx, "error message", logger.Err(err))
			return
		}
		if datas.ErrorCode == ERROR_CODE_VA_DUPLICATE {
			err = errors.SetErrorMessage(http.StatusConflict, "request failed to provider payment")
		} else {
			err = errors.SetErrorMessage(http.StatusInternalServerError, "request failed to provider payment")
		}
		logger.Error(ctx, "status not ok", logger.Err(err))
		return
	}

	err = json.Unmarshal(body, &resp)
	if err != nil {
		logger.Error(ctx, "error unmarshal", logger.Err(err))
		return
	}

	return
}

func (w *wrapper) UpdatePaymentVA(ctx context.Context, req UpdateVARequest, credential Credential) (resp VaResponse, err error) {
	path := fmt.Sprintf("/callback_virtual_accounts/%s", req.ID)
	logger.Info(ctx, "request", logger.String("path", w.Endpoint+path), logger.Any("req", req))

	header := setHeader(credential)
	body, status, err := w.client.Patch(ctx, path, header, req)
	if err != nil {
		err = errors.SetError(http.StatusInternalServerError, err.Error())
		logger.Error(ctx, err.Error())
		return
	}
	logger.Info(ctx, "response", logger.String("resp", string(body)))

	if status != http.StatusOK {
		var datas ErrorResponse
		_ = json.Unmarshal(body, &datas)
		if datas.Message != "" {
			err = errors.SetErrorMessage(http.StatusBadRequest, datas.Message)
			logger.Error(ctx, "error message", logger.Err(err))
			return
		}

		err = errors.SetErrorMessage(http.StatusInternalServerError, "request failed to provider payment")
		logger.Error(ctx, "status not ok", logger.Err(err))
		return
	}

	err = json.Unmarshal(body, &resp)
	if err != nil {
		logger.Error(ctx, "error unmarshal", logger.Err(err))
		return
	}

	return
}

func (w *wrapper) CheckStatusPaymentVA(ctx context.Context, paymentId string, credential Credential) (resp VaResponse, err error) {
	path := fmt.Sprintf("/callback_virtual_accounts/%v", paymentId)
	logger.Info(ctx, "request", logger.String("path", w.Endpoint+path), logger.Any("paymentId", paymentId))

	header := setHeader(credential)
	body, status, err := w.client.Get(ctx, path, header)
	if err != nil {
		err = errors.SetError(http.StatusInternalServerError, err.Error())
		logger.Error(ctx, err.Error())
		return
	}
	logger.Info(ctx, "response", logger.String("resp", string(body)))

	if status != http.StatusOK {
		var errData ErrorResponse
		_ = json.Unmarshal(body, &errData)
		errMessage := fmt.Sprintf("[xendit] Check Status Callback return status %d", status)
		if errData.ErrorCode == ERROR_CODE_VA_NOT_FOUND {
			err = errors.SetError(http.StatusNotFound, errMessage)
		} else {
			err = errors.SetError(http.StatusInternalServerError, errMessage)
		}
		logger.Error(ctx, "status not ok", logger.Err(err))
		return
	}

	err = json.Unmarshal(body, &resp)
	if err != nil {
		logger.Error(ctx, "error unmarshal", logger.Err(err))
		return
	}

	return
}

func (w *wrapper) CreatePaymentEwallet(ctx context.Context, req CreateEwalletRequest, credential Credential) (resp CreateEwalletResponse, err error) {
	path := "/ewallets/charges"
	logger.Info(ctx, "request", logger.String("path", w.Endpoint+path), logger.Any("req", req))

	header := setHeader(credential)
	body, status, err := w.client.Post(ctx, path, header, req)
	if err != nil {
		err = errors.SetError(http.StatusInternalServerError, err.Error())
		logger.Error(ctx, err.Error())
		return
	}
	logger.Info(ctx, "response", logger.String("resp", string(body)))

	if status != http.StatusAccepted {
		var datas ErrorResponse
		_ = json.Unmarshal(body, &datas)
		if datas.Message != "" {
			err = errors.SetErrorMessage(http.StatusBadRequest, datas.Message)
			logger.Error(ctx, "error message", logger.Err(err))
			return
		}

		err = errors.SetErrorMessage(http.StatusInternalServerError, "request failed to provider payment")
		logger.Error(ctx, "status not ok", logger.Err(err))
		return
	}

	err = json.Unmarshal(body, &resp)
	if err != nil {
		logger.Error(ctx, "error unmarshal", logger.Err(err))
		return
	}

	return
}

func (w *wrapper) CheckStatusPaymentEwallet(ctx context.Context, chargeId string, credential Credential) (resp CreateEwalletResponse, err error) {
	path := fmt.Sprintf("/ewallets/charges/%v", chargeId)
	logger.Info(ctx, "request", logger.String("path", w.Endpoint+path), logger.Any("req", chargeId))

	header := setHeader(credential)
	body, status, err := w.client.Get(ctx, path, header)
	if err != nil {
		err = errors.SetError(http.StatusInternalServerError, err.Error())
		logger.Error(ctx, "error request", logger.Err(err))
		return
	}
	logger.Info(ctx, "response", logger.String("resp", string(body)))

	if status != http.StatusOK {
		errMessage := fmt.Sprintf("[xendit] Check Status return status %d", status)
		err = errors.SetError(http.StatusInternalServerError, errMessage)
		logger.Error(ctx, "status not ok", logger.Err(err))
		return
	}

	err = json.Unmarshal(body, &resp)
	if err != nil {
		logger.Error(ctx, "error unmarshal", logger.Err(err))
		return
	}

	return
}

func (w *wrapper) CreatePaymentInvoice(ctx context.Context, req CreateInvoiceRequest, credential Credential) (resp CreateInvoiceResponse, err error) {
	path := "/v2/invoices"
	logger.Info(ctx, "request", logger.String("path", w.Endpoint+path), logger.Any("req", req))

	req.PaymentMethods = []string{"CREDIT_CARD"}

	header := setHeader(credential)
	body, status, err := w.client.Post(ctx, path, header, req)
	if err != nil {
		err = errors.SetError(http.StatusInternalServerError, err.Error())
		logger.Error(ctx, err.Error())
		return
	}
	logger.Info(ctx, "response", logger.String("resp", string(body)))

	if status != http.StatusOK {
		var datas ErrorResponse
		_ = json.Unmarshal(body, &datas)
		if datas.Message != "" {
			err = errors.SetErrorMessage(http.StatusBadRequest, datas.Message)
			logger.Error(ctx, "error message", logger.Err(err))
			return
		}

		err = errors.SetErrorMessage(http.StatusInternalServerError, "request failed to provider payment")
		logger.Error(ctx, "status not ok", logger.Err(err))
		return
	}

	err = json.Unmarshal(body, &resp)
	if err != nil {
		logger.Error(ctx, "error unmarshal", logger.Err(err))
		return
	}

	return
}

func (w *wrapper) CheckStatusPaymentInvoice(ctx context.Context, id string, credential Credential) (resp CreateInvoiceResponse, err error) {
	path := fmt.Sprintf("/v2/invoices/%v", id)
	logger.Info(ctx, "request", logger.String("path", w.Endpoint+path), logger.Any("req", id))

	header := setHeader(credential)
	body, status, err := w.client.Get(ctx, path, header)
	if err != nil {
		err = errors.SetError(http.StatusInternalServerError, err.Error())
		logger.Error(ctx, "error request", logger.Err(err))
		return
	}
	logger.Info(ctx, "response", logger.String("resp", string(body)))

	if status != http.StatusOK {
		errMessage := fmt.Sprintf("[xendit] Check Status return status %d", status)
		err = errors.SetError(http.StatusInternalServerError, errMessage)
		logger.Error(ctx, "status not ok", logger.Err(err))
		return
	}

	err = json.Unmarshal(body, &resp)
	if err != nil {
		logger.Error(ctx, err.Error())
		return
	}

	return
}

func (w *wrapper) CreateDisbursement(ctx context.Context, req DisbursementRequest, credential Credential) (resp DisbursementResponse, statusCode int, err error) {
	path := "/disbursements"
	logger.Info(ctx, "request", logger.String("path", w.Endpoint+path), logger.Any("req", req))

	header := setHeader(credential)
	body, statusCode, err := w.client.Post(ctx, path, header, req)
	if err != nil {
		err = errors.SetError(http.StatusInternalServerError, err.Error())
		logger.Error(ctx, "error request", logger.Err(err))
		return
	}
	logger.Info(ctx, "response", logger.String("resp", string(body)))

	err = json.Unmarshal(body, &resp)
	if err != nil {
		logger.Error(ctx, "error unmarshal", logger.Err(err))
		return
	}

	return
}

func (w *wrapper) CheckStatusDisbursement(ctx context.Context, id string, credential Credential) (resp DisbursementCheckStatusResponse, err error) {
	path := fmt.Sprintf("/disbursements/%v", id)
	logger.Info(ctx, "request", logger.String("path", w.Endpoint+path), logger.Any("req", id))

	header := setHeader(credential)
	body, status, err := w.client.Get(ctx, path, header)
	if err != nil {
		err = errors.SetError(http.StatusInternalServerError, err.Error())
		logger.Error(ctx, "error request", logger.Err(err))
		return
	}
	logger.Info(ctx, "response", logger.String("resp", string(body)))

	if status != http.StatusOK {
		errMessage := fmt.Sprintf("[xendit] Check Status return status %d", status)
		err = errors.SetError(http.StatusInternalServerError, errMessage)
		logger.Error(ctx, "status not ok", logger.Err(err))
		return
	}

	err = json.Unmarshal(body, &resp)
	if err != nil {
		logger.Error(ctx, "error unmarshal", logger.Err(err))
		return
	}

	return
}

func (w *wrapper) CheckByExternalId(ctx context.Context, externalId string, credential Credential) ([]DisbursementCheckStatusResponse, error) {
	path := fmt.Sprintf("/disbursements?external_id=%v", externalId)
	logger.Info(ctx, "request", logger.String("path", w.Endpoint+path), logger.Any("req", externalId))

	var resp []DisbursementCheckStatusResponse

	header := setHeader(credential)
	body, status, err := w.client.Get(ctx, path, header)
	if err != nil {
		err = errors.SetError(http.StatusInternalServerError, err.Error())
		logger.Error(ctx, "error request", logger.Err(err))
		return nil, err
	}
	logger.Info(ctx, "response", logger.String("resp", string(body)))

	if status != http.StatusOK {
		// not found because there are not yet have transactions
		if status == http.StatusNotFound {
			return nil, nil
		}

		errMessage := fmt.Sprintf("[xendit] Check Status return status %d", status)
		err = errors.SetError(http.StatusInternalServerError, errMessage)
		logger.Error(ctx, "status not ok", logger.Err(err))
		return nil, err
	}

	err = json.Unmarshal(body, &resp)
	if err != nil {
		logger.Error(ctx, "error unmarshal", logger.Err(err))
		return nil, err
	}

	return resp, nil
}

func (w *wrapper) CreateFixedPaymentCode(ctx context.Context, req CreateFixedPaymentCodeRequest, credential Credential) (resp FixedPaymentCodeResponse, err error) {
	path := "/fixed_payment_code"
	logger.Info(ctx, "request", logger.String("path", w.Endpoint+path), logger.Any("req", req))

	header := setHeader(credential)
	body, status, err := w.client.Post(ctx, path, header, req)
	if err != nil {
		err = errors.SetError(http.StatusInternalServerError, err.Error())
		logger.Error(ctx, "error request", logger.Err(err))
		return
	}
	logger.Info(ctx, "response", logger.String("resp", string(body)))

	if status != http.StatusOK {
		var datas ErrorResponse
		_ = json.Unmarshal(body, &datas)
		if datas.Message != "" {
			err = errors.SetErrorMessage(http.StatusBadRequest, datas.Message)
			logger.Error(ctx, "error message", logger.Err(err))
			return
		}

		err = errors.SetErrorMessage(http.StatusInternalServerError, "request failed to provider payment")
		logger.Error(ctx, "status not ok", logger.Err(err))
		return
	}

	err = json.Unmarshal(body, &resp)
	if err != nil {
		logger.Error(ctx, "error unmarshal", logger.Err(err))
		return
	}

	return
}

func (w *wrapper) UpdateFixedPaymentCode(ctx context.Context, req UpdateFixedPaymentCodeRequest, credential Credential) (resp FixedPaymentCodeResponse, err error) {
	path := fmt.Sprintf("/fixed_payment_code/%s", req.ID)
	logger.Info(ctx, "request", logger.String("path", w.Endpoint+path), logger.Any("req", req))

	header := setHeader(credential)
	body, status, err := w.client.Patch(ctx, path, header, req)
	if err != nil {
		err = errors.SetError(http.StatusInternalServerError, err.Error())
		logger.Error(ctx, err.Error())
		return
	}
	logger.Info(ctx, "response", logger.String("resp", string(body)))

	if status != http.StatusOK {
		var datas ErrorResponse
		_ = json.Unmarshal(body, &datas)
		if datas.Message != "" {
			err = errors.SetErrorMessage(http.StatusBadRequest, datas.Message)
			logger.Error(ctx, "error message", logger.Err(err))
			return
		}

		err = errors.SetErrorMessage(http.StatusInternalServerError, "request failed to provider payment")
		logger.Error(ctx, "status not ok", logger.Err(err))
		return
	}

	err = json.Unmarshal(body, &resp)
	if err != nil {
		logger.Error(ctx, "error unmarshal", logger.Err(err))
		return
	}

	return
}

func (w *wrapper) GetFixedPaymentCode(ctx context.Context, fixedPaymentCodeId string, credential Credential) (resp GetFixedPaymentCodeResponse, err error) {
	path := fmt.Sprintf("/fixed_payment_code/%s", fixedPaymentCodeId)
	logger.Info(ctx, "request", logger.String("path", w.Endpoint+path), logger.Any("req", fixedPaymentCodeId))

	header := setHeader(credential)
	body, status, err := w.client.Get(ctx, path, header)
	if err != nil {
		err = errors.SetError(http.StatusInternalServerError, err.Error())
		logger.Error(ctx, err.Error())
		return
	}
	logger.Info(ctx, "response", logger.String("resp", string(body)))

	if status != http.StatusOK {
		var datas ErrorResponse
		_ = json.Unmarshal(body, &datas)
		if datas.Message != "" {
			err = errors.SetErrorMessage(http.StatusBadRequest, datas.Message)
			logger.Error(ctx, "error request", logger.Err(err))
			return
		}

		err = errors.SetErrorMessage(http.StatusInternalServerError, "request failed to provider payment")
		logger.Error(ctx, "status not ok", logger.Err(err))
		return
	}

	err = json.Unmarshal(body, &resp)
	if err != nil {
		logger.Error(ctx, "error unmarshal", logger.Err(err))
		return
	}

	return
}

func (w *wrapper) GetFixedPaymentCodePayments(ctx context.Context, fixedPaymentCodeId string, credential Credential) (resp GetFixedPaymentCodePaymentsResponse, err error) {
	path := fmt.Sprintf("/fixed_payment_code/%s/payments", fixedPaymentCodeId)
	logger.Info(ctx, "request", logger.String("path", w.Endpoint+path), logger.Any("req", fixedPaymentCodeId))

	header := setHeader(credential)
	body, status, err := w.client.Get(ctx, path, header)
	if err != nil {
		err = errors.SetError(http.StatusInternalServerError, err.Error())
		logger.Error(ctx, "error request", logger.Err(err))
		return
	}
	logger.Info(ctx, "response", logger.String("resp", string(body)))

	if status != http.StatusOK {
		var datas ErrorResponse
		_ = json.Unmarshal(body, &datas)
		if datas.ErrorCode == "DATA_NOT_FOUND_ERROR" { // there is no payments, jsut return empty
			return GetFixedPaymentCodePaymentsResponse{}, nil
		}

		if datas.Message != "" {
			err = errors.SetErrorMessage(http.StatusBadRequest, datas.Message)
			logger.Error(ctx, "error message", logger.Err(err))
			return
		}

		err = errors.SetErrorMessage(http.StatusInternalServerError, "request failed to provider payment")
		logger.Error(ctx, "status not ok", logger.Err(err))
		return
	}

	err = json.Unmarshal(body, &resp)
	if err != nil {
		logger.Error(ctx, "error unmarshal", logger.Err(err))
		return
	}

	return
}
