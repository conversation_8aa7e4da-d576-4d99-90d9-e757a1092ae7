{"debug": true, "env": "development", "port": "6081", "host": "127.0.0.1", "appName": "payment-service", "version": "v1", "jwtKey": "SW5wpvMagKRarbuHm4m84buhh6b3z8Ebf5YfnGSheERce9xcxHzXT9UtgaGgVfQM", "basic_secret_key": "TyRsp573Frs9Ljd905WEnD6LiP754", "secret_key_company": "24Hts87532KlstrdsNgrys973Op34GhnEwL", "user_key": "gtwiTr6398sGrwis98w0LskdgnDwqrEewQV2cbn", "login_key": "ti87DEpQwNm234bYWQni983Bc75PsTWm", "tz": "Asia/Jakarta", "db": {"host": "**************", "port": "5555", "name": "payment", "user": "payment", "pass": "CandyPayPalsBejeweling", "ssl": "disable", "timezone": "Asia/Jakarta", "maxIdle": 30, "maxOpenConn": 50, "maxTimeIdleConn": 30, "searchpath": "public", "debug": true}, "rabbit": {"host": "**************", "port": "2222", "user": "payment", "password": "DentistContradistinctionsBurner", "exchange_name": "service-local", "queue_name": "payment-worker", "routing_key": "service-local.*"}, "redis": {"host": "**************", "port": "6666", "user": "", "password": "", "db": "0"}, "logger": {"file": true, "fileLocation": "./logs", "fileMaxAge": 30, "stdout": true}, "xfers": {"host": "", "sandbox": "https://sandbox-id.xfers.com/api/v4", "timeout": "10s", "proxy": "", "skiptls": false, "debug": true, "is_sandbox": true}, "nicepay": {"host": "https://www.nicepay.co.id/nicepay", "sandbox": "https://staging.nicepay.co.id/nicepay", "callback_url": "https://api-payment.nusatek.dev/nicepay/callback", "timeout": "10s", "proxy": "", "skiptls": false, "debug": true, "is_sandbox": true}, "xendit": {"host": "", "sandbox": "https://api.xendit.co", "timeout": "10s", "proxy": "", "skiptls": false, "debug": true, "is_sandbox": true}, "ottocash": {"host": "", "sandbox": "https://wolverine-dev.ottodigital.id/securepage/v2/rest/h2h/payment", "timeout": "10s", "proxy": "", "skiptls": false, "debug": true, "is_sandbox": true, "callback_success_url": "https://api-payment.nusatek.dev/ottocash/invoice/callback", "callback_failed_url": "https://api-payment.nusatek.dev/ottocash/invoice/callback", "callback_back_url": "https://api-payment.nusatek.dev/ottocash/invoice/callback"}, "doku": {"host": "", "sandbox": "https://api-sandbox.doku.com", "timeout": "10s", "proxy": "", "skiptls": false, "debug": true, "is_sandbox": true, "va_channel_mapping_api_path": {"bank bca": "/bca-virtual-account", "bank mandiri": "/mandiri-virtual-account", "bank bsi": "/bsm-virtual-account", "bank doku": "/doku-virtual-account", "bank bri": "/bri-virtual-account", "bank cimb": "/cimb-virtual-account", "bank permata": "/permata-virtual-account", "bank bni": "/bni-virtual-account", "bank danamon": "/danamon-virtual-account"}, "callback_url_va": "https://827a-182-1-89-254.ap.ngrok.io/doku/va/callback"}, "cashlez_backoffice": {"host": "https://secure.cashlez.com", "sandbox": "https://secure.cashlez.com", "username": "cashlez-it", "password": "Czit123!@#", "proxy": "", "skiptls": false, "debug": true, "is_sandbox": true}, "snap_bca": {"host": "", "sandbox": "https://devapi.klikbca.com", "timeout": "10s", "proxy": "", "skiptls": false, "debug": true, "is_sandbox": true}, "available_providers": ["Ottocash", "NicePay", "Xfers", "Xendit", "Midtrans", "Do<PERSON>", "<PERSON><PERSON><PERSON>", "SNAP BCA"], "s3": {"host": "ap-south-1.linodeobjects.com", "region": "ap-south-1", "access_key": "********************", "secret_key": "wr7mcFA8oBTbEHAaUgOD4ECAQcrgsoqsA6VJtjyw", "bucket": "nusatek-payment-dev"}, "snap": {"jwt": {"secret": "3P1AmbKBlejWvptb2wziCr6CCI93gFKg", "expired_seconds": 900}, "ignore_signature": true, "timeout_seconds": 5}, "mailgun": {"domain": "demo-finance.nusatek.id", "api_key": "**************************************************", "webhook_key": "2107485a67d19cac412898ca778c9114"}, "email": {"cashout": {"subject": "Laporan Disbursement Transaksi", "ccs": ["<EMAIL>", "<EMAIL>"], "template": "<PERSON><PERSON>a Yth.\r\n<partner_name>,\r\n<partner_payment_channel> , <partner_bank_account_number>\r\n\r\nTerlampir kami sampaikan laporan pencairan dana (disbursement) <product_name> pembayaran melalui <company_name> tanggal <transferred_date>.\r\nTotal dana dicairkan ke rekening adalah sebesar Rp <total_disbursement>\r\n\r\nApabila Bapak/Ibu membutuhkan konfirmasi, silahkan hubungi kami melalui WhatsApp 0859-2151-1958 (Rey)\r\n\r\nDemi<PERSON><PERSON> disampaikan, terima kasih atas kepercayaan Bapak/<PERSON>bu kepada kami."}}, "snap_nicepay": {"host": "https://dev.nicepay.co.id/nicepay", "db_process_url": "https://better-mouse-vastly.ngrok-free.app/nicepay/snap/callback", "debug": true}, "company_callback": {"cash_out": {"whitelist": ["NAAOC"]}}, "telegram_nusatek": {"domain": "demo-finance.nusatek.id", "auth": "**************************************************", "chat_id": "2107485a67d19cac412898ca778c9114"}}