package usecase

import (
	"context"
	"encoding/json"
	"fmt"
	"net/http"
	"strconv"
	"strings"
	"sync"
	"time"

	"github.com/go-redis/redis/v8"
	"github.com/google/uuid"
	"gorm.io/gorm"
	"repo.nusatek.id/moaja/backend/libraries/logger"
	messagebrokerConfig "repo.nusatek.id/moaja/backend/libraries/message-broker/config"
	messagebrokerMessage "repo.nusatek.id/moaja/backend/libraries/message-broker/message"
	errors "repo.nusatek.id/moaja/backend/libraries/service-utils/errors"

	"repo.nusatek.id/nusatek/payment/domain"
	"repo.nusatek.id/nusatek/payment/infrastructure/cashlez"
	"repo.nusatek.id/nusatek/payment/infrastructure/doku"
	infrastructure "repo.nusatek.id/nusatek/payment/infrastructure/messagebroker"
	"repo.nusatek.id/nusatek/payment/infrastructure/nicepay"
	"repo.nusatek.id/nusatek/payment/infrastructure/ottocash"
	"repo.nusatek.id/nusatek/payment/infrastructure/snapbca"
	"repo.nusatek.id/nusatek/payment/infrastructure/snapclient"
	"repo.nusatek.id/nusatek/payment/infrastructure/xendit"
	"repo.nusatek.id/nusatek/payment/infrastructure/xfers"
	"repo.nusatek.id/nusatek/payment/logutil"
	"repo.nusatek.id/nusatek/payment/modules/cash_in_transaction/entity"
	"repo.nusatek.id/nusatek/payment/utils"
	"repo.nusatek.id/nusatek/payment/utils/constants"
	"repo.nusatek.id/nusatek/payment/utils/str"
)

func (s *defaultCashinTransaction) getProviderSecret(ctx context.Context, invoiceNumber string) (cashIn *domain.CashInTransactions, secret string, err error) {
	cashIn, err = s.getCacheCashInTransaction(ctx, invoiceNumber)
	if err != nil {
		logger.Error(ctx, err.Error())
		return
	}
	companyId := strconv.Itoa(cashIn.CompanyID)
	providerId := strconv.Itoa(cashIn.PaymentProviderID)
	companyMapping, err := s.companyMappingService.GetCompanyProviderMappingByCompanyIdAndProviderId(ctx, companyId, providerId)
	if err != nil {
		logger.Error(ctx, err.Error())
		return
	}

	secret = companyMapping.ProviderSecrets
	return
}

func (s *defaultCashinTransaction) getCredentialCashInXenditCallback(ctx context.Context, invoiceNumber string) (resp xendit.Credential, cashIn *domain.CashInTransactions, err error) {
	cashIn, secret, err := s.getProviderSecret(ctx, invoiceNumber)
	if err != nil {
		return
	}
	credential, errRes := xendit.GetXenditCredential(secret)
	if errRes != nil {
		err = errors.SetErrorMessage(http.StatusInternalServerError, errRes.Error())
		logger.Error(ctx, err.Error())
		return
	}
	resp = credential
	return
}

func (s *defaultCashinTransaction) getCredentialCashInXfersCallback(ctx context.Context, invoiceNumber string) (resp xfers.Credential, cashIn *domain.CashInTransactions, err error) {
	cashIn, secret, err := s.getProviderSecret(ctx, invoiceNumber)
	if err != nil {
		return
	}
	credential, errRes := xfers.GetXfersCredential(secret)
	if errRes != nil {
		err = errors.SetErrorMessage(http.StatusInternalServerError, errRes.Error())
		logger.Error(ctx, err.Error())
		return
	}
	resp = credential
	return
}

func (s *defaultCashinTransaction) getCredentialCashInOttocashCallback(ctx context.Context, invoiceNumber string) (resp ottocash.Credential, cashIn *domain.CashInTransactions, err error) {
	cashIn, secret, err := s.getProviderSecret(ctx, invoiceNumber)
	if err != nil {
		return
	}
	credential, errRes := ottocash.GetOttocashCredential(secret)
	if errRes != nil {
		err = errors.SetErrorMessage(http.StatusInternalServerError, errRes.Error())
		logger.Error(ctx, err.Error())
		return
	}
	resp = credential
	return
}

func (s *defaultCashinTransaction) getCredentialCashInNicepayCallback(ctx context.Context, invoiceNumber string) (resp nicepay.Credential, cashIn *domain.CashInTransactions, err error) {
	cashIn, secret, err := s.getProviderSecret(ctx, invoiceNumber)
	if err != nil {
		return
	}
	credential, errRes := nicepay.GetNicepayCredential(secret)
	if errRes != nil {
		err = errors.SetErrorMessage(http.StatusInternalServerError, errRes.Error())
		logger.Error(ctx, err.Error())
		return
	}
	resp = credential
	return
}

func (s *defaultCashinTransaction) getCredentialCashInSnapNicepayCallback(ctx context.Context, invoiceNumber string) (resp snapclient.NicepayCred, cashIn *domain.CashInTransactions, err error) {
	cashIn, secret, err := s.getProviderSecret(ctx, invoiceNumber)
	if err != nil {
		return
	}
	var credential snapclient.NicepayCred
	errRes := snapclient.GetSnapCredentialCB(secret, &credential)
	if errRes != nil {
		err = errors.SetErrorMessage(http.StatusInternalServerError, errRes.Error())
		logger.Error(ctx, err.Error())
		return
	}
	resp = credential
	return
}

func (s *defaultCashinTransaction) getCredentialCashInDokuCallback(ctx context.Context, invoiceNumber string) (resp doku.Credential, cashIn *domain.CashInTransactions, err error) {
	cashIn, secret, err := s.getProviderSecret(ctx, invoiceNumber)
	if err != nil {
		err = errors.SetErrorMessage(http.StatusInternalServerError, err.Error())
		logger.Error(ctx, err.Error())
		return
	}
	credential, errRes := doku.GetDokuCredential(secret)
	if errRes != nil {
		err = errors.SetErrorMessage(http.StatusInternalServerError, errRes.Error())
		logger.Error(ctx, err.Error())
		return
	}
	resp = credential
	return
}

func (s *defaultCashinTransaction) getCredentialBcaSnap(ctx context.Context, invoiceNumber string) (resp snapbca.Credential, cashIn *domain.CashInTransactions, err error) {
	cashIn, secret, err := s.getProviderSecret(ctx, invoiceNumber)
	if err != nil {
		err = errors.SetErrorMessage(http.StatusInternalServerError, err.Error())
		logger.Error(ctx, err.Error())
		return
	}

	credential, errRes := snapbca.GetCredential(secret)
	if errRes != nil {
		err = errors.SetErrorMessage(http.StatusInternalServerError, errRes.Error())
		logger.Error(ctx, err.Error())
		return
	}
	resp = credential
	return
}

func (s *defaultCashinTransaction) getCacheCashInTransaction(ctx context.Context, invoiceNumber string) (resp *domain.CashInTransactions, err error) {
	key := fmt.Sprintf("%v-%v", constants.CacheCashInTransaction, invoiceNumber)
	cache, errRes := s.cache.Get(ctx, key).Result()
	if errRes != nil {
		if errRes != redis.Nil {
			err = errors.SetErrorMessage(http.StatusInternalServerError, errRes.Error())
			logger.Error(ctx, err.Error())
			return
		}
	}

	switch true {
	case cache == "":
		prod, errRes := s.cashinRepo.GetCashinTransactionByInvoiceNumber(ctx, invoiceNumber)
		if errRes != nil {
			err = errors.SetErrorMessage(http.StatusNotFound, "cash in transaction not found")
			logger.Error(ctx, err.Error())
			return
		}
		err = s.setCacheCashinTransaction(ctx, prod)
		if err != nil {
			return
		}
		resp = prod

	default:
		var prod domain.CashInTransactions
		errRes = json.Unmarshal([]byte(cache), &prod)
		if errRes != nil {
			err = errors.SetErrorMessage(http.StatusInternalServerError, errRes.Error())
			logger.Error(ctx, err.Error())
			return
		}
		resp = &prod
	}
	return
}

func (s *defaultCashinTransaction) setCacheCashinTransaction(ctx context.Context, req *domain.CashInTransactions) (err error) {
	prodByte, errRes := json.Marshal(req)
	if errRes != nil {
		err = errors.SetErrorMessage(http.StatusInternalServerError, errRes.Error())
		logger.Error(ctx, err.Error())
		return
	}
	key := fmt.Sprintf("%v-%v", constants.CacheCashInTransaction, req.InvoiceNumber)
	errRes = s.cache.Set(ctx, key, string(prodByte), 24*time.Hour).Err()
	if errRes != nil {
		err = errors.SetErrorMessage(http.StatusInternalServerError, errRes.Error())
		logger.Error(ctx, err.Error())
		return
	}
	return
}

// nolint: unused
// func (s *defaultCashinTransaction) deleteCacheCashInTransaction(ctx context.Context, invoiceNumber string) (err error) {
// 	key := fmt.Sprintf("%v-%v", constants.CacheCashInTransaction, invoiceNumber)
// 	err = s.cache.Del(ctx, key).Err()
// 	if err != nil {
// 		err = errors.SetError(http.StatusInternalServerError, err.Error())
// 		logger.Error(ctx, err.Error())
// 		return
// 	}
// 	return
// }

func (s *defaultCashinTransaction) syncToCashOut(ctx context.Context, partnerId string, totalAmount float64, sequence int, tx *gorm.DB) (resp *domain.CashOutTransactions, err error) {
	partner, err := s.partnerService.GetPartnerById(ctx, partnerId)
	if err != nil {
		err = errors.SetErrorMessage(http.StatusNotFound, err.Error())
		logger.Error(ctx, err.Error())
		return
	}
	company, err := s.companyService.GetCompanyById(ctx, strconv.Itoa(partner.CompanyID))
	if err != nil {
		err = errors.SetErrorMessage(http.StatusNotFound, err.Error())
		logger.Error(ctx, err.Error())
		return
	}
	channelResp, errRes := s.channelService.GetPaymentChannelById(ctx, strconv.Itoa(partner.PaymentChannelId))
	if errRes != nil {
		err = errors.SetErrorMessage(http.StatusNotFound, errRes.Error())
		logger.Error(ctx, err.Error())
		return
	}

	year, month, _ := time.Now().Date()

	cashOutTransaction := new(domain.CashOutTransactions)
	cashOutTransaction.CompanyID = company.ID
	cashOutTransaction.PartnerID = partner.ID
	cashOutTransaction.PartnerName = partner.Name
	cashOutTransaction.PartnerAccountName = partner.ArAccount
	cashOutTransaction.PartnerAccountNumber = partner.ApAccount
	cashOutTransaction.PartnerBankName = channelResp.Name
	cashOutTransaction.PaymentChannelID = channelResp.ID
	cashOutTransaction.BatchNumber = "CO/" + company.Code + "/" + fmt.Sprint(year) + "/" + fmt.Sprint(int(month)) + "/" + fmt.Sprint(sequence)
	cashOutTransaction.Total = totalAmount
	cashOutTransaction.PgReferenceInformation = "{}"
	cashOutTransaction.Status = constants.CashOutStatusReady

	history := &domain.CashOutTransactionHistories{
		Description: "sync from cash-in transcation",
		Status:      constants.CashOutStatusReady,
	}

	cashOutTransaction.CashOutTransactionHistories = history
	err = s.cashinRepo.CreateCashOutTransaction(ctx, cashOutTransaction, tx)
	if err != nil {
		err = errors.SetError(http.StatusInternalServerError, err.Error())
		logger.Error(ctx, err.Error())
		return
	}

	resp = cashOutTransaction
	return
}

// nolint: unused
// func (s *defaultCashinTransaction) republishMessage(tofic string, req interface{}) {
// 	reqByte, _ := json.Marshal(req)
// 	msq := messagebrokerMessage.Message{
// 		Type:    messagebrokerConfig.CONTENT_TYPE_JSON,
// 		Content: reqByte,
// 	}
// 	infrastructure.PublishMessage(tofic, &msq)
// }

func (s *defaultCashinTransaction) composeResponseCashInSearch(ctx context.Context, cashIn *[]entity.CashinList) (resp *[]entity.CashinList, err error) {
	for i := 0; i < len(*cashIn); i++ {
		if (*cashIn)[i].SettlementDate != "0" {
			unixString, errRes := utils.ParseUnixTime((*cashIn)[i].SettlementDate)
			if errRes != nil {
				logger.Error(ctx, errRes.Error())
			}
			(*cashIn)[i].SettlementDate = unixString
		}

		if (*cashIn)[i].SettlementDate == "0" {
			(*cashIn)[i].SettlementDate = ""
		}

		(*cashIn)[i].GrandTotal = (*cashIn)[i].Amount + (*cashIn)[i].ProductFee
		subtotal := (*cashIn)[i].Amount + (*cashIn)[i].Voucher
		(*cashIn)[i].CompanyProductCashoutFee = utils.GetCompProductCashoutFee(subtotal, (*cashIn)[i].CompanyProductCashoutFeeFixValue, (*cashIn)[i].CompanyProductCashoutFeePercentage)

		if (*cashIn)[i].LogoURL != "" {
			(*cashIn)[i].LogoURL, _ = s.s3.GetURL((*cashIn)[i].LogoURL)
		}
	}

	resp = cashIn
	return
}

func (s *defaultCashinTransaction) batchingCashInToCashOutProcess(ctx context.Context) (err error) {
	loc := utils.GetLocalTime()
	date := loc.Format("2006-01-02")
	items, err := s.cashinRepo.FilterCashInItemsByDate(ctx, date)
	if err != nil {
		err = errors.SetError(http.StatusInternalServerError, err.Error())
		logger.Error(ctx, err.Error())
		return
	}

	var numArrPartner []int
	for k := 0; k < len(*items); k++ {
		numArrPartner = append(numArrPartner, (*items)[k].PartnerId)
	}

	partners := utils.RemoveDuplicateValues(numArrPartner)
	cashOutLast, err := s.cashinRepo.GetLastCashOutTransactions(ctx)
	if err != nil {
		err = errors.SetError(http.StatusInternalServerError, err.Error())
		logger.Error(ctx, err.Error())
		return
	}

	tx := s.cashinRepo.BeginTrans()
	defer tx.Rollback()

	for i := 0; i < len(partners); i++ {
		var amount float64
		var itemPartner []domain.CashInTransactionItems
		for y := 0; y < len(*items); y++ {
			if partners[i] == (*items)[y].PartnerId {
				amount = amount + (*items)[y].Amount
				itemPartner = append(itemPartner, (*items)[y])
			}
		}
		sequence := cashOutLast.ID + i
		cashOut, errRes := s.syncToCashOut(ctx, strconv.Itoa(partners[i]), amount, sequence, tx)
		if errRes != nil {
			return
		}
		for k := 0; k < len(itemPartner); k++ {
			cashOutTransactionDetail := new(domain.CashOutTransactionDetails)
			cashOutTransactionDetail.CashOutTransactionID = cashOut.ID
			cashOutTransactionDetail.CashInTransactionItemID = itemPartner[k].ID
			err = s.cashinRepo.CreateCashOutTransactionDetail(ctx, cashOutTransactionDetail, tx)
			if err != nil {
				err = errors.SetError(http.StatusInternalServerError, err.Error())
				logger.Error(ctx, err.Error())
				return
			}

			itemPartner[k].IsCashout = true
			err = s.cashinRepo.UpdateCashinTransactionItems(ctx, &itemPartner[k], tx)
			if err != nil {
				err = errors.SetError(http.StatusInternalServerError, err.Error())
				logger.Error(ctx, err.Error())
				return
			}
		}

		amount = 0
		itemPartner = nil
	}

	tx.Commit()
	return
}

// Called in scheduler, dynamic function for building cash out transaction from paid cash in
func (s *defaultCashinTransaction) BuildCashOutTransaction(ctx context.Context) (err error) {
	corrId := uuid.New().String()
	ctx = logger.SetCorrelationId(ctx, corrId)
	logger.Info(ctx, "BuildCashOutTransaction Start", logger.String("time", time.Now().Format(time.RFC3339)))

	// Get all cash in item transaction that SLA is LTE current timestamp & is_cashout is false
	items, err := s.cashinRepo.FilterUnforwardedCashInItems(ctx)
	if err != nil {
		err = errors.SetError(http.StatusInternalServerError, err.Error())
		logger.Error(ctx, err.Error())
		return
	}

	outstandingTtems, err := s.cashinRepo.FilterUnforwardedOutstandingCashInItems(ctx)
	if err != nil {
		err = errors.SetError(http.StatusInternalServerError, err.Error())
		logger.Error(ctx, err.Error())
		return
	}

	items = append(items, outstandingTtems...) // merge items

	if len(items) == 0 {
		logger.Error(ctx, "empty data")
		return
	}

	// Group data by partner
	var itemIds []int
	var cashInIds []int
	var partnerIds []int
	var productIds []int
	itemPartners := make(map[string][]domain.CashInTransactionItemsCashout)
	for _, v := range items {
		itemIds = append(itemIds, v.ID)
		cashInIds = append(cashInIds, v.CashInTransactionID)
		partnerIds = append(partnerIds, v.PartnerId)
		productIds = append(productIds, v.CashInTransactionCompanyProductID)
		itemPartnerKey := fmt.Sprintf("%d:%d", v.PartnerId, v.CashInTransactionCompanyProductID) // partner id: product id
		itemPartners[itemPartnerKey] = append(itemPartners[itemPartnerKey], v)

	}

	// Select partners bulk
	partners, err := s.partnerService.SelectByIds(ctx, partnerIds)
	if err != nil {
		err = errors.SetError(http.StatusInternalServerError, err.Error())
		logger.Error(ctx, err.Error())
		return
	}
	var companyIds, paymentChannelIds []int
	partnersMap := make(map[int]domain.Partners)
	for _, v := range partners {
		companyIds = append(companyIds, v.CompanyID)
		paymentChannelIds = append(paymentChannelIds, v.PaymentChannelId)
		partnersMap[v.ID] = v
	}

	companies, err := s.companyService.SelectByIds(ctx, companyIds)
	if err != nil {
		err = errors.SetError(http.StatusInternalServerError, err.Error())
		logger.Error(ctx, err.Error())
		return
	}
	companiesMap := make(map[int]domain.Companies)
	for _, v := range companies {
		companiesMap[v.ID] = v
	}

	paymentChannels, err := s.channelService.SelectByIds(ctx, paymentChannelIds)
	if err != nil {
		err = errors.SetError(http.StatusInternalServerError, err.Error())
		logger.Error(ctx, err.Error())
		return
	}
	paymentChannelsMap := make(map[int]domain.PaymentChannels)
	for _, v := range paymentChannels {
		paymentChannelsMap[v.ID] = v
	}

	products, err := s.companyProductService.SelectByIds(ctx, productIds)
	if err != nil {
		err = errors.SetError(http.StatusInternalServerError, err.Error())
		logger.Error(ctx, err.Error())
		return
	}
	productMap := make(map[int]domain.CompanyProducts)
	for _, v := range products {
		productMap[v.ID] = v
	}

	// Select bulk cashout transaction by partner_id and status
	cashouts, err := s.cashinRepo.SelectCashOutByPartnerIds(ctx, partnerIds, constants.CashOutStatusReady)
	if err != nil {
		err = errors.SetError(http.StatusInternalServerError, err.Error())
		logger.Error(ctx, err.Error())
		return
	}
	cashoutPartner := make(map[string]domain.CashOutTransactions)
	for _, v := range cashouts {
		if v.CompanyProductID == nil {
			continue // if company product id nil it means that is old data, so skip, TBC??
		}

		// always get first latest cashout transaction
		if co, exist := cashoutPartner[fmt.Sprintf("%d:%d", v.PartnerID, *v.CompanyProductID)]; exist && co.ID > v.ID {
			continue
		}

		cashoutPartner[fmt.Sprintf("%d:%d", v.PartnerID, *v.CompanyProductID)] = v // partner id: product id
	}

	// Get last ID for batch sequence
	cashOutLast, err := s.cashinRepo.GetLastCashOutTransactions(ctx)
	if err != nil {
		err = errors.SetError(http.StatusInternalServerError, err.Error())
		logger.Error(ctx, err.Error())
		return
	}

	var sequence int
	if cashOutLast != nil {
		sequence = cashOutLast.ID
	}

	reconciledAt := time.Now()

	var cashOutCallbacks []domain.CashOutCallback
	// Create or update cash out
	tx := s.cashinRepo.BeginTrans()
	defer tx.Rollback()
	for k, v := range itemPartners {
		strs := strings.Split(k, ":") // partner id: product id
		partnerId, _ := strconv.Atoi(strs[0])
		productId, _ := strconv.Atoi(strs[1])

		var cashout *domain.CashOutTransactions
		company := companiesMap[partnersMap[partnerId].CompanyID]
		partner := partnersMap[partnerId]
		product := productMap[productId]

		var callbackOperation string
		if cashoutPartner[k].ID == 0 {
			// Create new cashout transaction if there isn't ready record
			// Insert data into cash_out_transactions & cash_out_transaction_details table
			sequence++
			cashout, err = s.createToCashOut(ctx, tx, sequence, companiesMap[partnersMap[partnerId].CompanyID],
				paymentChannelsMap[partnersMap[partnerId].PaymentChannelId], partnersMap[partnerId],
				v, product)
			if err != nil {
				err = errors.SetError(http.StatusInternalServerError, err.Error())
				logger.Error(ctx, err.Error())
				return
			}
			callbackOperation = domain.CashoutCallbackCreated
		} else {
			// Append items into existing cashout transaction if there's any ready record
			// Insert data into cash_out_transaction_details & update amount in cash_out_transactions table
			cashout, err = s.updateToCashOut(ctx, tx, cashoutPartner[k],
				product, v)
			if err != nil {
				err = errors.SetError(http.StatusInternalServerError, err.Error())
				logger.Error(ctx, err.Error())
				return
			}
			callbackOperation = domain.CashoutCallbackUpdated
		}

		cashoutCallback := domain.CashOutCallback{
			Operation:   callbackOperation,
			CompanyCode: company.Code,
			ProductName: product.ProductName,
			ProductCode: product.Code,
			PartnerName: partner.Name,
			PartnerCode: partner.Code,
			BatchNumber: cashout.BatchNumber,
			Status:      constants.CashOutStatusReady,
		}

		for _, item := range v {
			cashoutCallback.CashoutItems = append(cashoutCallback.CashoutItems, domain.CashOutCallbackCashoutItems{
				CashIn: domain.CashOutCallbackCashin{
					InvoiceNumber: item.CashInInvoiceNumber,
					//status and payment status will be defined below
				},
				RefInvoiceNumber: item.RefInvoiceNumber,
				ItemName:         item.ItemName,
				Status:           constants.CashInItemStatusReconciled, // status supposed to be reconciled
			})
		}
		cashOutCallbacks = append(cashOutCallbacks, cashoutCallback)
	}

	// Update is_cashout into true in cash_in_transaction_items table
	// cashout used for flaggin cashin items that already processed on cashout
	err = s.cashinRepo.UpdateIsCashoutAndStatusBulk(ctx, tx, itemIds, constants.CashInItemStatusReconciled)
	if err != nil {
		err = errors.SetError(http.StatusInternalServerError, err.Error())
		logger.Error(ctx, err.Error())
		return
	}

	// create histories and define cash in status
	// get item count for define status
	cashInWithItemCounts, err := s.cashinRepo.GetAllByIDsWithItemCount(ctx, tx, cashInIds)
	if err != nil {
		err = errors.SetError(http.StatusInternalServerError, err.Error())
		logger.Error(ctx, err.Error())
		return err
	}

	var histories []domain.CashInTransactionHistories
	var cashInStatusMap = make(map[string]map[string]string)
	for _, cashIn := range cashInWithItemCounts {
		//define status
		var cashInstatus = constants.DefineCashInStatusFromItemStatuses(cashIn.Status, cashIn.ItemStatuses.Strings())
		logger.Info(ctx, "debug status build cashout", logger.String("status", cashIn.Status), logger.String("nextStatus", cashInstatus))
		cashInStatusMap[cashIn.InvoiceNumber] = make(map[string]string)
		cashInStatusMap[cashIn.InvoiceNumber]["paymentStatus"] = cashIn.PaymentStatus
		cashInStatusMap[cashIn.InvoiceNumber]["status"] = cashInstatus
		if cashInstatus == cashIn.Status {
			continue
		}

		err = s.cashinRepo.UpdateStatusCashInTransaction(ctx, tx, cashIn.ID, cashInstatus)
		if err != nil {
			err = errors.SetError(http.StatusInternalServerError, fmt.Sprintf("update cash in status error %v", err))
			logger.Error(ctx, err.Error())
			return err
		}
		// apend histories
		histories = append(histories, domain.CashInTransactionHistories{
			CashInTransactionID:    cashIn.ID,
			Description:            "build cashout",
			PaymentStatus:          "",
			Status:                 cashInstatus,
			VirtualAccount:         "",
			InvoiceNumber:          cashIn.InvoiceNumber,
			ProviderId:             cashIn.PaymentProviderID,
			ChannelId:              cashIn.PaymentChannelID,
			PgReferenceInformation: "{}",
			CreatedAt:              reconciledAt,
			UpdatedAt:              reconciledAt,
		})
	}

	if len(histories) > 0 {
		// create histories bulk
		err = s.cashinRepo.CreateBulkCashinTransactionHistory(ctx, tx, histories)
		if err != nil {
			err = errors.SetError(http.StatusInternalServerError, fmt.Sprintf("create bulk cash in histories %v", err))
			logger.Error(ctx, err.Error())
			return err
		}
	}

	err = tx.Commit().Error
	if err != nil {
		err = errors.SetErrorMessage(http.StatusInternalServerError, err.Error())
		logger.Error(ctx, err.Error())
		return err
	}

	// replace cash in status
	for i := range cashOutCallbacks {
		for j := range cashOutCallbacks[i].CashoutItems {
			invoiceNumber := cashOutCallbacks[i].CashoutItems[j].CashIn.InvoiceNumber
			if status, exist := cashInStatusMap[invoiceNumber]; exist {
				cashOutCallbacks[i].CashoutItems[j].CashIn.PaymentStatus = status["paymentStatus"]
				cashOutCallbacks[i].CashoutItems[j].CashIn.Status = status["status"]
			}
		}
	}

	wg := new(sync.WaitGroup)
	for _, cbData := range cashOutCallbacks {
		wg.Add(1)
		go func(cbData domain.CashOutCallback) {
			defer wg.Done()
			errPub := s.PublishCashOut(ctx, cbData)
			if errPub != nil {
				logger.Error(ctx, fmt.Sprintf("error publish cashin item to client %v", errPub))
			}
		}(cbData)
	}
	wg.Wait()

	logger.Info(ctx, "BuildCashOutTransaction Finish", logger.String("time", time.Now().Format(time.RFC3339)))

	return
}

func (s *defaultCashinTransaction) createToCashOut(ctx context.Context, tx *gorm.DB, sequence int, company domain.Companies, paymentChannel domain.PaymentChannels, partner domain.Partners, itemPartners []domain.CashInTransactionItemsCashout, comProduct domain.CompanyProducts) (resp *domain.CashOutTransactions, err error) {
	var totalAmount float64
	var totalCashIn int
	var cashInIds []int64

	cashInMap := make(map[int]bool)
	for _, v := range itemPartners {
		totalAmount += v.Amount
		_, exist := cashInMap[v.CashInTransactionID]
		if exist {
			continue
		}
		cashInMap[v.CashInTransactionID] = true
		cashInIds = append(cashInIds, int64(v.CashInTransactionID))
		totalCashIn++
	}

	year, month, _ := time.Now().Date()
	cashOutTransaction := new(domain.CashOutTransactions)
	cashOutTransaction.CompanyID = company.ID
	cashOutTransaction.PartnerID = partner.ID
	cashOutTransaction.PartnerName = partner.Name
	cashOutTransaction.PartnerAccountName = partner.ArAccount
	cashOutTransaction.PartnerAccountNumber = partner.ApAccount
	cashOutTransaction.PartnerBankName = paymentChannel.Name
	cashOutTransaction.PaymentChannelID = paymentChannel.ID
	cashOutTransaction.CompanyProductID = &comProduct.ID
	cashOutTransaction.BatchNumber = "CO/" + company.Code + "/" + fmt.Sprint(year) + "/" + fmt.Sprint(int(month)) + "/" + fmt.Sprint(sequence)
	cashOutTransaction.Total = totalAmount
	cashOutTransaction.PgReferenceInformation = "{}"
	cashOutTransaction.Status = constants.CashOutStatusReady
	// cashOutTransaction.SetAdminFeeFromProduct(comProduct, totalCashIn)

	fees, err := s.cashinRepo.GetTotalCompanyProductFees(ctx, cashInIds)
	cashOutTransaction.AdminFee = fees

	history := &domain.CashOutTransactionHistories{
		Description: "sync from cash-in transcation",
		Status:      constants.CashOutStatusReady,
	}

	cashOutTransaction.CashOutTransactionHistories = history
	err = s.cashinRepo.CreateCashOutTransaction(ctx, cashOutTransaction, tx)
	if err != nil {
		err = errors.SetError(http.StatusInternalServerError, err.Error())
		logger.Error(ctx, err.Error())
		return
	}

	var cashoutDetails []domain.CashOutTransactionDetails
	for _, v := range itemPartners {
		cashoutDetails = append(cashoutDetails, domain.CashOutTransactionDetails{
			CashOutTransactionID:    cashOutTransaction.ID,
			CashInTransactionItemID: v.ID,
		})
	}
	err = s.cashinRepo.CreateBulkCashOutTransactionDetail(ctx, &cashoutDetails, tx)
	if err != nil {
		err = errors.SetError(http.StatusInternalServerError, err.Error())
		logger.Error(ctx, err.Error())
		return
	}

	resp = cashOutTransaction
	return
}

func (s *defaultCashinTransaction) updateToCashOut(ctx context.Context, tx *gorm.DB, cashout domain.CashOutTransactions, _ domain.CompanyProducts, itemPartners []domain.CashInTransactionItemsCashout) (resp *domain.CashOutTransactions, err error) {
	// get current item cash out
	var totalCashIn int
	var totalItemAmount float64
	var cashInIds []int64
	items, err := s.cashinRepo.GetCashInByCashoutID(ctx, cashout.ID)
	if err != nil {
		err = errors.SetError(http.StatusInternalServerError, err.Error())
		logger.Error(ctx, err.Error())
		return
	}
	cashInMap := make(map[int]bool)
	for _, v := range items {
		totalItemAmount += v.Amount
		_, exist := cashInMap[v.CashInTransactionID]
		if exist {
			continue
		}
		cashInIds = append(cashInIds, int64(v.CashInTransactionID))
		cashInMap[v.CashInTransactionID] = true
		totalCashIn++
	}

	var totalAmount float64

	for _, v := range itemPartners {
		totalAmount += v.Amount
		_, exist := cashInMap[v.CashInTransactionID]
		if exist {
			continue
		}

		cashInMap[v.CashInTransactionID] = true
		totalCashIn++
	}

	cashOutTransaction := new(domain.CashOutTransactions)
	cashOutTransaction.ID = cashout.ID
	cashOutTransaction.Total = totalItemAmount + totalAmount
	// cashOutTransaction.SetAdminFeeFromProduct(comProduct, totalCashIn)

	fees, _ := s.cashinRepo.GetTotalCompanyProductFees(ctx, cashInIds)

	cashOutTransaction.AdminFee = fees
	err = s.cashinRepo.UpdatePartialCashoutTransaction(ctx, tx, cashOutTransaction)
	if err != nil {
		err = errors.SetError(http.StatusInternalServerError, err.Error())
		logger.Error(ctx, err.Error())
		return
	}

	var cashoutDetails []domain.CashOutTransactionDetails
	for _, v := range itemPartners {
		cashoutDetails = append(cashoutDetails, domain.CashOutTransactionDetails{
			CashOutTransactionID:    cashOutTransaction.ID,
			CashInTransactionItemID: v.ID,
		})
	}
	err = s.cashinRepo.CreateBulkCashOutTransactionDetail(ctx, &cashoutDetails, tx)
	if err != nil {
		err = errors.SetError(http.StatusInternalServerError, err.Error())
		logger.Error(ctx, err.Error())
		return
	}

	resp = cashOutTransaction
	return
}

// Called in scheduler, check payment status for pending cash in transaction
func (s *defaultCashinTransaction) CheckPendingCashInTransaction(ctx context.Context) (err error) {
	logger.Info(ctx, "CheckPendingCashInTransaction scheduler start")

	// Get all pending cash in transaction
	cashIn, err := s.cashinRepo.GetCashinByStatus(ctx, constants.StatusPending)
	if err != nil {
		err = errors.SetError(http.StatusInternalServerError, err.Error())
		logger.Error(ctx, err.Error())
		return
	}

	var counterMax = 0
	// Send message into rabbitMQ topic based on payment channel used
	for _, v := range *cashIn {
		originCorrId := logger.GetCorrelationId(ctx)
		ctx = logger.SetCorrelationId(ctx, uuid.NewString())
		ctx = logger.SetCtxData(ctx, map[string]interface{}{
			"originCorrId":  originCorrId,
			"cashInId":      v.ID,
			"invoiceNumber": v.InvoiceNumber,
		})
		if counterMax == 10 {
			// set couner max = 0
			counterMax = 0
			// set sleep/waiting 1 second to next
			time.Sleep(1 * time.Second)
		} else {
			// counter now
			fmt.Println("counter run===========")
			counterMax++
		}

		if v.PgReferenceInformation == "" {
			continue
		}

		var keyPublis string
		var resByte []byte
		if strings.Contains(strings.ToLower(v.PaymentProviderName), constants.ProviderXfers) {
			if v.PaymentChannelType == constants.TrxVirtualAccount {
				var callback xfers.PaymentResponse
				err = json.Unmarshal([]byte(v.PgReferenceInformation), &callback)
				if err != nil {
					err = errors.SetError(http.StatusInternalServerError, err.Error())
					logger.Error(ctx, err.Error())
					return
				}
				resByte, _ = json.Marshal(callback)

				keyPublis = constants.CallbackCashInXfersVa
			}
		} else if strings.Contains(strings.ToLower(v.PaymentProviderName), constants.ProviderXendit) {
			if v.PaymentChannelType == constants.TrxVirtualAccount {
				var callback xendit.CallbackVAResponse
				err = json.Unmarshal([]byte(v.PgReferenceInformation), &callback)
				if err != nil {
					err = errors.SetError(http.StatusInternalServerError, err.Error())
					logger.Error(ctx, err.Error())
					return
				}
				resByte, _ = json.Marshal(callback)

				keyPublis = constants.CallbackCashInXenditVa
			} else if v.PaymentChannelType == constants.TrxEwallet {
				var callback xendit.CallbackEwalletResponse
				err = json.Unmarshal([]byte(v.PgReferenceInformation), &callback)
				if err != nil {
					err = errors.SetError(http.StatusInternalServerError, err.Error())
					logger.Error(ctx, err.Error())
					return
				}
				resByte, _ = json.Marshal(callback)

				keyPublis = constants.CallbackCashInXenditEwalet
			} else if v.PaymentChannelType == constants.TrxCc {
				var callback xendit.CallbackInvoiceResponse
				err = json.Unmarshal([]byte(v.PgReferenceInformation), &callback)
				if err != nil {
					err = errors.SetError(http.StatusInternalServerError, err.Error())
					logger.Error(ctx, err.Error())
					return
				}
				resByte, _ = json.Marshal(callback)

				keyPublis = constants.CallbackCashInXenditInvoice
			} else if v.PaymentChannelType == constants.TrxRetail {
				var callback xendit.CallbackFixedPaymentCode
				err = json.Unmarshal([]byte(v.PgReferenceInformation), &callback)
				if err != nil {
					err = errors.SetError(http.StatusInternalServerError, err.Error())
					logger.Error(ctx, err.Error())
					return
				}
				resByte, _ = json.Marshal(callback)

				keyPublis = constants.CallbackCashInXenditFixedPaymentCode
			}
		} else if strings.Contains(strings.ToLower(v.PaymentProviderName), constants.ProviderOttocash) {
			if v.PaymentChannelType == constants.TrxEwallet {
				var callback ottocash.InquiryCallback
				err = json.Unmarshal([]byte(v.PgReferenceInformation), &callback)
				if err != nil {
					err = errors.SetError(http.StatusInternalServerError, err.Error())
					logger.Error(ctx, err.Error())
					return
				}
				callback.TrxID = v.InvoiceNumber
				resByte, _ = json.Marshal(callback)

				keyPublis = constants.CallbackCashInOttocashEwallet
			}
		} else if strings.ToLower(v.PaymentProviderName) == constants.ProviderNicePay {
			if v.PaymentChannelType == constants.TrxVirtualAccount {
				var callback nicepay.NotificationRequest
				// json response registration different with notification (callback) request from nicepay
				// but both have mandatory field for inquiry
				// tXid, referenceNo, amt
				if v.PaymentStatus == constants.PaymentDraft {
					var res nicepay.RegistrationResponse
					err = json.Unmarshal([]byte(v.PgReferenceInformation), &res)
					if err != nil {
						err = errors.SetError(http.StatusInternalServerError, err.Error())
						logger.Error(ctx, err.Error())
						return
					}
					callback = nicepay.NotificationRequest{
						TXid:        res.TXid,
						ReferenceNo: res.ReferenceNo,
						Amt:         str.ToFloat64(res.Amt),
					}
				}
				resByte, _ = json.Marshal(callback)

				keyPublis = constants.CallbackCashInNicePay
			}
		} else if strings.Contains(strings.ToLower(v.PaymentProviderName), constants.ProviderDoku) {
			if v.PaymentChannelType == constants.TrxVirtualAccount {
				var vaData doku.VAPaymentCodeRes
				err = json.Unmarshal([]byte(v.PgReferenceInformation), &vaData)
				if err != nil {
					err = errors.SetError(http.StatusInternalServerError, err.Error())
					logger.Error(ctx, err.Error())
					return
				}
				var callback doku.VACallback
				callback.Order.InvoiceNumber = vaData.Order.InvoiceNumber
				resByte, _ = json.Marshal(callback)

				keyPublis = constants.CallbackCashInDokuVA
			}
		} else if strings.Contains(strings.ToLower(v.PaymentProviderName), constants.ProviderCashlez) {
			if v.PaymentChannelType == constants.TrxOfflinePG {
				var data cashlez.CallbackOfflinePG
				err = json.Unmarshal([]byte(v.PgReferenceInformation), &data)
				if err != nil {
					err = errors.SetError(http.StatusInternalServerError, err.Error())
					logger.Error(ctx, err.Error())
					return
				}
				if len(data.MerchantTrxID) == 0 { // edge case when there is something wrong with pg_reference_information
					data.MerchantTrxID = v.InvoiceNumber
				}
				resByte, _ = json.Marshal(data)

				keyPublis = constants.CallbackCashInCashlezOfflinePg
			}
		} else if strings.Contains(strings.ToLower(v.PaymentProviderName), constants.ProviderBankBCA) { // SNAP scheduler
			if v.PaymentChannelType == constants.TrxSNAP {
				// is used domain.SnapCbCashInTransaction
				// var data domain.SnapCbCashInTransaction
				resByte = []byte(v.PgReferenceInformation)

				keyPublis = constants.CallbackCashInBankBcaSnap
			}
		} else if strings.ToLower(v.PaymentProviderName) == constants.ProviderSnapNicePay {
			if v.PaymentChannelType == constants.TrxVirtualAccount {
				// json response registration different with notification (callback) request from nicepay
				// but both have mandatory field for inquiry
				// tXid, referenceNo, amt
				if v.PaymentStatus == constants.PaymentDraft {
					var histInfo snapclient.NicepayVaHistoryInfo
					err = json.Unmarshal([]byte(v.PgReferenceInformation), &histInfo)
					if err != nil {
						err = errors.SetError(http.StatusInternalServerError, err.Error())
						logger.Error(ctx, err.Error())
						return
					}
					callback := snapclient.NicepaySnapNotifRequest{
						NicepayNotifAdditionalVARequest: snapclient.NicepayNotifAdditionalVARequest{
							VacctNo: histInfo.VacctNo,
						},
						PayMethod:   snapclient.NicepayPayMethodVirtualAccount,
						TXid:        histInfo.TXidVA,
						ReferenceNo: histInfo.TrxID,
						Amt:         histInfo.TotalAmount.Value,
					}
					resByte, _ = json.Marshal(callback)
					keyPublis = constants.CallbackCashInSnapNicePay
				}
			}
		} else {
			continue
		}

		msq := messagebrokerMessage.Message{
			Headers: logutil.SetAmqpTableFromCtx(ctx),
			Type:    messagebrokerConfig.CONTENT_TYPE_JSON,
			Content: resByte,
		}
		go infrastructure.PublishMessage(keyPublis, &msq)
	}

	logger.Info(ctx, "CheckPendingCashInTransaction scheduler end", logger.Int("total", len(*cashIn)))

	return err
}
