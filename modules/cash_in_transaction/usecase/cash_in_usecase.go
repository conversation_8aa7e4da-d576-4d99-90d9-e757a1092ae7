package usecase

import (
	"context"
	"encoding/json"
	"fmt"
	"net/http"
	"strconv"
	"strings"
	"time"

	"github.com/xuri/excelize/v2"
	"gorm.io/gorm"
	"repo.nusatek.id/moaja/backend/libraries/logger"
	messagebrokerConfig "repo.nusatek.id/moaja/backend/libraries/message-broker/config"
	messagebrokerMessage "repo.nusatek.id/moaja/backend/libraries/message-broker/message"
	errors "repo.nusatek.id/moaja/backend/libraries/service-utils/errors"

	"repo.nusatek.id/nusatek/payment/domain"
	"repo.nusatek.id/nusatek/payment/infrastructure/cashlez"
	"repo.nusatek.id/nusatek/payment/infrastructure/doku"
	infrastructure "repo.nusatek.id/nusatek/payment/infrastructure/messagebroker"
	"repo.nusatek.id/nusatek/payment/infrastructure/nicepay"
	"repo.nusatek.id/nusatek/payment/infrastructure/ottocash"
	"repo.nusatek.id/nusatek/payment/infrastructure/snapbca"
	"repo.nusatek.id/nusatek/payment/infrastructure/snapclient"
	"repo.nusatek.id/nusatek/payment/infrastructure/xendit"
	"repo.nusatek.id/nusatek/payment/infrastructure/xfers"
	"repo.nusatek.id/nusatek/payment/logutil"
	"repo.nusatek.id/nusatek/payment/modules/cash_in_transaction/entity"
	"repo.nusatek.id/nusatek/payment/utils"
	"repo.nusatek.id/nusatek/payment/utils/constants"
	"repo.nusatek.id/nusatek/payment/utils/interfacepkg"
	"repo.nusatek.id/nusatek/payment/utils/str"
	"repo.nusatek.id/nusatek/payment/utils/timepkg"
)

func (s *defaultCashinTransaction) SearchCashinTranscation(ctx context.Context, param entity.CashinListParam) (resp *[]entity.CashinList, totalData int64, err error) {
	resp, totalData, err = s.cashinRepo.GetCashinTranscationList(ctx, param)
	if err != nil {
		err = errors.SetError(http.StatusInternalServerError, err.Error())
		logger.Error(ctx, err.Error())
		return
	}

	resp, err = s.composeResponseCashInSearch(ctx, resp)
	return
}

func (s *defaultCashinTransaction) GetByIDDetail(ctx context.Context, id int) (res domain.CashInTransactionDetail, err error) {
	res, err = s.cashinRepo.GetCashinTransactionByIdDetail(ctx, strconv.Itoa(id))
	if err != nil {
		logger.Error(ctx, err.Error())
		if err == gorm.ErrRecordNotFound {
			err = errors.SetError(http.StatusNotFound, err.Error())
			return
		}
		err = errors.SetError(http.StatusInternalServerError, err.Error())
		return
	}

	return
}

func (s *defaultCashinTransaction) GetListCashinTransactionAPI(ctx context.Context, param entity.CashinListParam, auth string) (resp *[]entity.CashinList, totalData int64, err error) {
	company, err := s.companyService.ValidatorCompanySecret(ctx, auth)
	if err != nil {
		return
	}

	param.Pagination.Key = "company_id"
	param.Pagination.Value = strconv.Itoa(company.ID)
	resp, totalData, err = s.cashinRepo.GetCashinTranscationList(ctx, param)
	if err != nil {
		err = errors.SetError(http.StatusInternalServerError, err.Error())
		logger.Error(ctx, err.Error())
		return
	}

	resp, err = s.composeResponseCashInSearch(ctx, resp)
	return
}

func (s *defaultCashinTransaction) CashinPaymentActivityLogByTransactionID(ctx context.Context, paginate utils.Pagination, id string) (resp []entity.CashinActivityLog, totalData int64, err error) {
	history, totalData, err := s.cashinRepo.GetListTranscationHistoryByCashInTransactionID(ctx, paginate, id)
	if err != nil {
		err = errors.SetErrorMessage(http.StatusInternalServerError, err.Error())
		logger.Error(ctx, err.Error())
		return
	}
	var historyList []entity.CashinActivityLog
	for i := 0; i < len(*history); i++ {
		channel, errRes := s.channelService.GetPaymentChannelById(ctx, strconv.Itoa((*history)[i].ChannelID))
		if errRes != nil {
			return
		}
		provider, errRes := s.providerService.GetPaymentProviderById(ctx, strconv.Itoa((*history)[i].ProviderID))
		if errRes != nil {
			return
		}
		res := entity.CashinActivityLog{
			ID:                     (*history)[i].ID,
			PaymentProvider:        provider.Name,
			PaymentChannel:         channel.Name,
			VirtualAccountNumber:   (*history)[i].VirtualAccount,
			PaymentStatus:          (*history)[i].Status,
			CreatedAt:              (*history)[i].CreatedAt,
			Description:            (*history)[i].Description,
			PgReferenceInformation: (*history)[i].PgReferenceInformation,
		}
		historyList = append(historyList, res)
	}
	resp = historyList
	return
}

func (s *defaultCashinTransaction) GetListItemsByTransactionID(ctx context.Context, paginate utils.Pagination, transactionID int64) (resp []domain.CashInTransactionItemComplete, totalData int64, err error) {

	resp, totalData, err = s.cashinRepo.GetListItemsByTransactionID(ctx, paginate, transactionID)
	if err != nil && err != gorm.ErrRecordNotFound {
		err = errors.SetErrorMessage(http.StatusInternalServerError, "error get cash in transaction items")
		logger.Error(ctx, err.Error())
		return
	}

	return
}

func (s *defaultCashinTransaction) PushCallbackXenditCashin(ctx context.Context, token string, req interface{}, paymentType string) (err error) {
	logger.Info(ctx, "PushCallbackXenditCashin", logger.Any("req", req))
	var credential xendit.Credential
	var reqByte []byte
	var keyPublis string

	switch paymentType {
	case constants.TrxVirtualAccount:
		var callback xendit.CallbackVAResponse
		reqByte, _ = json.Marshal(req)
		err = json.Unmarshal(reqByte, &callback)
		if err != nil {
			err = errors.SetError(http.StatusInternalServerError, err.Error())
			logger.Error(ctx, err.Error())
			return
		}
		credential, _, err = s.getCredentialCashInXenditCallback(ctx, callback.ExternalID)
		if err != nil {
			return
		}
		keyPublis = constants.CallbackCashInXenditVa

	case constants.TrxEwallet:
		var callback xendit.CallbackEwalletResponse
		reqByte, _ = json.Marshal(req)
		err = json.Unmarshal(reqByte, &callback)
		if err != nil {
			err = errors.SetError(http.StatusInternalServerError, err.Error())
			logger.Error(ctx, err.Error())
			return
		}
		credential, _, err = s.getCredentialCashInXenditCallback(ctx, callback.Data.ReferenceId)
		if err != nil {
			return
		}

		keyPublis = constants.CallbackCashInXenditEwalet

	case constants.TrxCc:
		var callback xendit.CallbackInvoiceResponse
		reqByte, _ = json.Marshal(req)
		err = json.Unmarshal(reqByte, &callback)
		if err != nil {
			err = errors.SetError(http.StatusInternalServerError, err.Error())
			logger.Error(ctx, err.Error())
			return
		}
		credential, _, err = s.getCredentialCashInXenditCallback(ctx, callback.ExternalID)
		if err != nil {
			return
		}

		keyPublis = constants.CallbackCashInXenditInvoice
	case constants.TrxRetail:
		var callback xendit.CallbackFixedPaymentCode
		reqByte, _ = json.Marshal(req)
		err = json.Unmarshal(reqByte, &callback)
		if err != nil {
			err = errors.SetError(http.StatusInternalServerError, err.Error())
			logger.Error(ctx, err.Error())
			return
		}
		credential, _, err = s.getCredentialCashInXenditCallback(ctx, callback.ExternalID)
		if err != nil {
			return
		}

		keyPublis = constants.CallbackCashInXenditFixedPaymentCode
	}

	if credential.CallbackToken != token {
		err = errors.SetErrorMessage(http.StatusUnauthorized, "token authorization not valid")
		logger.Error(ctx, err.Error())
		return
	}

	msq := messagebrokerMessage.Message{
		Headers: logutil.SetAmqpTableFromCtx(ctx),
		Type:    messagebrokerConfig.CONTENT_TYPE_JSON,
		Content: reqByte,
	}
	infrastructure.PublishMessage(keyPublis, &msq)

	return
}

func (s *defaultCashinTransaction) PushCallbackXfersCashin(ctx context.Context, signature, paymentType string, req interface{}) (err error) {
	logger.Info(ctx, "PushCallbackXfersCashin", logger.Any("req", req))
	var credential xfers.Credential
	var reqByte []byte
	var keyPublis string

	switch paymentType {
	case constants.TrxVirtualAccount:
		var callback xfers.PaymentResponse
		reqByte, _ = json.Marshal(req)
		err = json.Unmarshal(reqByte, &callback)
		if err != nil {
			err = errors.SetError(http.StatusInternalServerError, err.Error())
			logger.Error(ctx, err.Error())
			return
		}

		credential, _, err = s.getCredentialCashInXfersCallback(ctx, callback.Data.PaymentDataResp.ReferenceId)
		if err != nil {
			return
		}
		keyPublis = constants.CallbackCashInXfersVa

	default:
		err = errors.SetErrorMessage(http.StatusInternalServerError, "payment type not found in provider")
		logger.Error(ctx, err.Error())
		return
	}

	mac := utils.GenerateSignatureHmac256(string(reqByte), credential.SignKeyAccept)
	if mac != signature {
		err = errors.SetErrorMessage(http.StatusBadRequest, "signature not valid")
		logger.Error(ctx, err.Error())
		return
	}

	msq := messagebrokerMessage.Message{
		Headers: logutil.SetAmqpTableFromCtx(ctx),
		Type:    messagebrokerConfig.CONTENT_TYPE_JSON,
		Content: reqByte,
	}
	infrastructure.PublishMessage(keyPublis, &msq)

	return
}

func (s *defaultCashinTransaction) PushCallbackNicePayCashin(ctx context.Context, req interface{}) (err error) {
	reqByte, err := json.Marshal(req)
	if err != nil {
		err = errors.SetErrorMessage(http.StatusInternalServerError, err.Error())
		logger.Error(ctx, err.Error())
		return
	}

	msq := messagebrokerMessage.Message{
		Headers: logutil.SetAmqpTableFromCtx(ctx),
		Type:    messagebrokerConfig.CONTENT_TYPE_JSON,
		Content: reqByte,
	}
	infrastructure.PublishMessage(constants.CallbackCashInNicePay, &msq)

	return
}

func (s *defaultCashinTransaction) PushCallbackSnapNicePayCashin(ctx context.Context, req interface{}) (err error) {
	reqByte, err := json.Marshal(req)
	if err != nil {
		err = errors.SetErrorMessage(http.StatusInternalServerError, err.Error())
		logger.Error(ctx, err.Error())
		return
	}

	msq := messagebrokerMessage.Message{
		Headers: logutil.SetAmqpTableFromCtx(ctx),
		Type:    messagebrokerConfig.CONTENT_TYPE_JSON,
		Content: reqByte,
	}
	infrastructure.PublishMessage(constants.CallbackCashInSnapNicePay, &msq)

	return
}

func (s *defaultCashinTransaction) PushCallbackOttocashCashin(ctx context.Context, signature, timestamp string, req interface{}) (err error) {
	logger.Info(ctx, "PushCallbackOttocashCashin", logger.Any("req", req))
	var credential ottocash.Credential
	var reqByte []byte
	var keyPublis string

	var callback ottocash.InquiryCallback
	reqByte, _ = json.Marshal(req)
	err = json.Unmarshal(reqByte, &callback)
	if err != nil {
		err = errors.SetError(http.StatusInternalServerError, err.Error())
		logger.Error(ctx, err.Error())
		return
	}

	credential, _, err = s.getCredentialCashInOttocashCallback(ctx, callback.TrxID)
	if err != nil {
		logger.Error(ctx, err.Error())
		return
	}
	keyPublis = constants.CallbackCashInOttocashEwallet

	mac := ottocash.SetSignature(ctx, credential.CallbackSecretKey, callback, timestamp)
	if mac != signature {
		err = errors.SetErrorMessage(http.StatusBadRequest, "signature not valid "+mac+" != "+signature)
		logger.Error(ctx, err.Error())
		return
	}

	msq := messagebrokerMessage.Message{
		Headers: logutil.SetAmqpTableFromCtx(ctx),
		Type:    messagebrokerConfig.CONTENT_TYPE_JSON,
		Content: reqByte,
	}
	infrastructure.PublishMessage(keyPublis, &msq)
	return
}

func (s *defaultCashinTransaction) PushCallbackDokuCashin(ctx context.Context, header http.Header, reqByte []byte, paymentType string) (err error) {
	logger.Info(ctx, "PushCallbackDokuCashin", logger.String("req", string(reqByte)))
	var credential doku.Credential
	var keyPublis string

	signature := header.Get(doku.HEADER_SIGNATURE)
	var requestTarget string
	switch paymentType {
	case constants.TrxVirtualAccount:
		var callback doku.VACallback
		err = json.Unmarshal(reqByte, &callback)
		if err != nil {
			err = errors.SetErrorMessage(http.StatusInternalServerError, err.Error())
			logger.Error(ctx, err.Error())
			return
		}
		credential, _, err = s.getCredentialCashInDokuCallback(ctx, callback.Order.InvoiceNumber)
		if err != nil {
			logger.Error(ctx, "[DOKU] error get credential")
			return
		}
		keyPublis = constants.CallbackCashInDokuVA
		requestTarget = s.dokuRest.Config().VirtualAccountNotificationURL.Path

	default:
		logger.Error(ctx, "[DOKU] PushCallbackDokuCashin invalid payment type")
	}

	sc := doku.SignatureComponent{
		Credential:       credential,
		RequestTarget:    requestTarget,
		RequestTimestamp: header.Get(doku.HEADER_REQUEST_TIMESTAMP),
		RequestID:        header.Get(doku.HEADER_REQUEST_ID),
		RequestBody:      reqByte,
	}
	err = s.dokuRest.VerifyCallbackSignature(ctx, sc, signature)
	if err != nil {
		logger.Error(ctx, err.Error())
		err = errors.SetErrorMessage(http.StatusUnauthorized, "Invalid Signature")
		return
	}

	msq := messagebrokerMessage.Message{
		Headers: logutil.SetAmqpTableFromCtx(ctx),
		Type:    messagebrokerConfig.CONTENT_TYPE_JSON,
		Content: reqByte,
	}
	infrastructure.PublishMessage(keyPublis, &msq)

	return
}

func (s *defaultCashinTransaction) PushCallbackCashlez(ctx context.Context, header http.Header, reqByte []byte, paymentType string) (err error) {
	logger.Info(ctx, "PushCallbackCashlez", logger.String("req", string(reqByte)))
	var keyPublis string

	switch paymentType {
	case constants.TrxOfflinePG:

		// validate basic auth company
		auth := header.Get("Authorization")
		_, err = s.companyService.ValidatorCompanySecret(ctx, auth)
		if err != nil {
			logger.Error(ctx, err.Error())
			err = errors.SetErrorMessage(http.StatusUnauthorized, "invalid auth")
			return
		}

		var callback cashlez.CallbackOfflinePG
		err = json.Unmarshal(reqByte, &callback)
		if err != nil {
			err = errors.SetErrorMessage(http.StatusInternalServerError, err.Error())
			logger.Error(ctx, err.Error())
			return
		}
		keyPublis = constants.CallbackCashInCashlezOfflinePg

	default:
		logger.Error(ctx, "[Cashlez] PushCallbackCashlez invalid payment type")
	}

	msq := messagebrokerMessage.Message{
		Headers: logutil.SetAmqpTableFromCtx(ctx),
		Type:    messagebrokerConfig.CONTENT_TYPE_JSON,
		Content: reqByte,
	}
	infrastructure.PublishMessage(keyPublis, &msq)
	return
}

// TO DO: (David) implement backoff retry for cashInTransaction Process
func (s *defaultCashinTransaction) CallbackXfersCashinTranscationProcess(ctx context.Context, req xfers.PaymentResponse) {
	go func() {
		if err := s.trxReqLogService.UpdateResourceCtx(ctx, req.Data.PaymentDataResp.ReferenceId, logutil.ResourceTypeCashIn); err != nil {
			logger.Error(ctx, err.Error())
		}
	}()

	credential, cashIn, err := s.getCredentialCashInXfersCallback(ctx, req.Data.PaymentDataResp.ReferenceId)
	if err != nil {
		return
	}
	respXfer, err := s.xfersRest.CheckStatus(ctx, req.Data.ID, credential)
	if err == nil {
		err = s.updateStatusXfersCashinTransaction(ctx, respXfer, cashIn)
		if err != nil {
			logger.Error(ctx, err.Error())
			return
		}
		return
	}

}

func (s *defaultCashinTransaction) CallbackNicePayCashInTransactionProcess(ctx context.Context, req nicepay.NotificationRequest) {
	go func() {
		if err := s.trxReqLogService.UpdateResourceCtx(ctx, req.ReferenceNo, logutil.ResourceTypeCashIn); err != nil {
			logger.Error(ctx, err.Error())
		}
	}()

	credential, cashIn, err := s.getCredentialCashInNicepayCallback(ctx, req.ReferenceNo)
	if err != nil {
		return
	}

	loc, _ := time.LoadLocation("Asia/Jakarta") // Asia/Jakarta / UTC ??
	txTime := time.Now().In(loc)
	inquiry := &nicepay.InquiryRequest{
		TimeStamp:     txTime.Format(nicepay.DateTimeFormat),
		MerchantToken: nicepay.GetMerchantToken(txTime.Format(nicepay.DateTimeFormat), credential, req.ReferenceNo, fmt.Sprintf("%.0f", req.Amt)),
		ReferenceNo:   req.ReferenceNo,
		TXid:          req.TXid,
		Amt:           fmt.Sprintf("%.0f", req.Amt),
		IMid:          credential.MerchantID,
	}

	resp, err := s.nicePayRest.Inquiry(ctx, inquiry, credential)
	if err == nil {
		switch resp.PayMethod {
		case nicepay.PayMethodVirtualAccount:
			err = s.updateStatusNicePayVACashinTransaction(ctx, resp, cashIn)
			if err != nil {
				logger.Error(ctx, err.Error())
				return
			}
			return
		default:
			logger.Info(ctx, "[cashIn callback] nicepay invalid payment method")
			return
		}
	}

}

func (s *defaultCashinTransaction) CallbackSnapNicePayCashInTransactionProcess(ctx context.Context, req snapclient.NicepaySnapNotifRequest) {
	go func() {
		if err := s.trxReqLogService.UpdateResourceCtx(ctx, req.ReferenceNo, logutil.ResourceTypeCashIn); err != nil {
			logger.Error(ctx, err.Error())
		}
	}()

	credential, cashIn, err := s.getCredentialCashInSnapNicepayCallback(ctx, req.ReferenceNo)
	if err != nil {
		return
	}

	/* merchantToken SHA256(iMid+tXid+amt+merchantKey)
	validate merchant token
	merchantToken := fmt.Sprintf("%s%s%s%s", credential.ClientID, req.TXid, req.Amt, credential.ClientSecret)
	merchantTokenB := sha256.Sum256([]byte(merchantToken))
	merchantToken = string(merchantTokenB[:])
	// no need to check merchant token since this also used for check pending scheduler
	*/

	hist, err := s.cashinRepo.GetCashinTransactionHistoryByPaymentStatus(ctx, cashIn.ID, constants.PaymentDraft)
	if err != nil {
		logger.Error(ctx, err.Error())
		return
	}

	var partnerServiceId, cutomerNo string
	if len(hist.VirtualAccount) >= 16 {
		// untuk parameter partnerServiceId bisa diambil dari 8 angka pertama dari parameter virtualAccountNo
		partnerServiceId = hist.VirtualAccount[:8]
		// untuk parameter customerNo bisa di ambil dari sisa angka setelah 8 angka pertama dari parameter virtualAccountNo
		cutomerNo = hist.VirtualAccount[8:]
	}

	var histInfo snapclient.NicepayVaHistoryInfo
	_ = json.Unmarshal([]byte(hist.PgReferenceInformation), &histInfo)

	reqStatus := snapclient.TransferVaStatusReq{
		PartnerServiceID: partnerServiceId,
		CustomerNo:       cutomerNo,
		VirtualAccountNo: hist.VirtualAccount,
		InquiryRequestID: histInfo.HeaderExternalId,
		AdditionalInfo: snapclient.AdditionalInfo{
			Data: map[string]interface{}{
				"totalAmount": histInfo.TotalAmount,
				"trxId":       histInfo.TrxID,
				"tXidVA":      histInfo.TXidVA,
			},
		},
	}
	hreq := snapclient.HTrxReq{
		XPartnerId: credential.ClientID,
		ChannelId:  credential.ChannelId,
	}
	resp, err := s.snapNicepay.TransferVaStatus(ctx, credential, hreq, &reqStatus)
	if err == nil {
		switch req.PayMethod {
		case snapclient.NicepayPayMethodVirtualAccount:
			err = s.updateStatusSnapNicePayVACashinTransaction(ctx, resp, cashIn)
			if err != nil {
				logger.Error(ctx, err.Error())
				return
			}
			return
		default:
			logger.Info(ctx, "[cashIn callback] invalid snap nicepay invalid payment method")
			return
		}
	}

}

func (s *defaultCashinTransaction) CallbackXenditCashinTranscationProcess(ctx context.Context, referenceId, paymentId, paymentType string, body []byte) {
	go func() {
		if err := s.trxReqLogService.UpdateResourceCtx(ctx, referenceId, logutil.ResourceTypeCashIn); err != nil {
			logger.Error(ctx, err.Error())
		}
	}()

	credential, cashIn, err := s.getCredentialCashInXenditCallback(ctx, referenceId)
	if err != nil {
		return
	}

	switch paymentType {
	case constants.TrxVirtualAccount:
		resp, err := s.xenditRest.CheckStatusPaymentVA(ctx, paymentId, credential)
		if err == nil {
			err = s.updateStatusXenditVACashinTransaction(ctx, resp, cashIn)
			if err != nil {
				logger.Error(ctx, err.Error())
				return
			}
			return
		}
		logger.Error(ctx, fmt.Sprintf("xendit.CheckStatusPaymentVA %s", err))

	case constants.TrxEwallet:
		resp, err := s.xenditRest.CheckStatusPaymentEwallet(ctx, paymentId, credential)
		if err == nil {
			err = s.updateStatusXenditEwalletCashinTransaction(ctx, resp, cashIn)
			if err != nil {
				logger.Error(ctx, err.Error())
				return
			}
			return
		}
		logger.Error(ctx, fmt.Sprintf("xendit.CheckStatusPaymentEwallet %s", err))

	case constants.TrxCc:
		resp, err := s.xenditRest.CheckStatusPaymentInvoice(ctx, paymentId, credential)
		if err == nil {
			err = s.updateStatusXenditInvoiceCashinTransaction(ctx, resp, cashIn)
			if err != nil {
				logger.Error(ctx, err.Error())
				return
			}
			return
		}
		logger.Error(ctx, fmt.Sprintf("xendit.CheckStatusPaymentInvoice %s", err))

	case constants.TrxRetail:
		resp, err := s.xenditRest.GetFixedPaymentCode(ctx, paymentId, credential)
		if err == nil {
			// check payments
			paymentsResp, err := s.xenditRest.GetFixedPaymentCodePayments(ctx, paymentId, credential)
			if err != nil {
				logger.Error(ctx, err.Error())
				return
			}

			err = s.updateStatusXenditFixedPaymentCodeCashinTransaction(ctx, resp, paymentsResp, cashIn)
			if err != nil {
				logger.Error(ctx, err.Error())
				return
			}
			return
		}
		logger.Error(ctx, fmt.Sprintf("xendit.GetFixedPaymentCode %s", err))
	}

}

func (s *defaultCashinTransaction) CallbackOttocashCashinTranscationProcess(ctx context.Context, referenceId, paymentId, responseCode string, body []byte) {
	go func() {
		if err := s.trxReqLogService.UpdateResourceCtx(ctx, referenceId, logutil.ResourceTypeCashIn); err != nil {
			logger.Error(ctx, err.Error())
		}
	}()

	credential, cashIn, err := s.getCredentialCashInOttocashCallback(ctx, referenceId)
	if err != nil {
		return
	}

	resp, err := s.ottocashRest.CheckStatus(ctx, ottocash.CheckStatusRequest{ReferenceNumber: paymentId}, credential)
	if err == nil {
		err = s.updateStatusOttocashCashinTransaction(ctx, resp.ErrorCode, cashIn, body)
		if err != nil {
			logger.Error(ctx, err.Error())
			return
		}
		return
	}
}

func (s *defaultCashinTransaction) CallbackDokuCashinTranscationProcess(ctx context.Context, referenceId, paymentId, paymentType string, body []byte) {
	go func() {
		if err := s.trxReqLogService.UpdateResourceCtx(ctx, referenceId, logutil.ResourceTypeCashIn); err != nil {
			logger.Error(ctx, err.Error())
		}
	}()

	credential, cashIn, err := s.getCredentialCashInDokuCallback(ctx, referenceId)
	if err != nil {
		logger.Error(ctx, "[DOKU] transaction process, error get credential")
		return
	}

	// check order status
	rawResp, resp, err := s.dokuRest.OrderStatus(ctx, credential, referenceId)
	if err != nil {
		logger.Error(ctx, err.Error(), logger.String("rawResp", string(rawResp)))
		return
	}

	switch paymentType {
	case constants.TrxVirtualAccount:
		err = s.updateStatusDokuVACashinTransaction(ctx, resp, body, cashIn)
		if err != nil {
			logger.Error(ctx, err.Error())
			return
		}
		return

	default:
		logger.Error(ctx, "invalid payment type")
	}

}

func (s *defaultCashinTransaction) CallbackCashlezCashinTranscationProcess(ctx context.Context, referenceId, paymentId, paymentType string, body []byte) {
	go func() {
		if err := s.trxReqLogService.UpdateResourceCtx(ctx, referenceId, logutil.ResourceTypeCashIn); err != nil {
			logger.Error(ctx, err.Error())
		}
	}()

	cashIn, err := s.getCacheCashInTransaction(ctx, referenceId)
	if err != nil {
		logger.Error(ctx, err.Error())
		return
	}

	switch paymentType {
	case constants.TrxOfflinePG:
		var cbData cashlez.CallbackOfflinePG
		if err := json.Unmarshal(body, &cbData); err != nil {
			logger.Error(ctx, err.Error())
		}
		var dataCashlez cashlez.TransactionAdvanceSearchRespData

		logger.Info(ctx, fmt.Sprintf("cashlez transaction id %v, trying to get data from cashlez", cbData.MerchantTrxID))
		searchReq := cashlez.TransactionAdvanceSearchReq{
			// MerchantTxid: cbData.MerchantTrxID,
			Order: "txid",
			Sort:  "desc",
			Limit: 1,
		}
		if len(cbData.CashlezTransactionID) > 0 {
			searchReq.Txid = cbData.CashlezTransactionID
		} else {
			searchReq.MerchantTxid = cbData.MerchantTrxID
		}
		cashlezData, err := s.cashlezRest.TransactionAdvanceSearch(ctx, 1, searchReq)
		if err != nil {
			logger.Error(ctx, err.Error())
			return
		}
		if len(cashlezData.Data) <= 0 { // make the expired transaction can be process
			logger.Warn(ctx, fmt.Sprintf("cashlez with transaction %s is not exist", cbData.MerchantTrxID))
		} else {
			dataCashlez = cashlezData.Data[0]
		}

		err = s.updateStatusCashlezOfflinePgCashinTransaction(ctx, dataCashlez, body, cashIn)
		if err != nil {
			logger.Error(ctx, err.Error())
			return
		}
		return

	default:
		logger.Error(ctx, "invalid payment type")
	}

}

func (s *defaultCashinTransaction) CallbackBankBcaSnapCashinTranscationProcess(ctx context.Context, cb domain.SnapCbCashInTransaction) {

	var cashIn *domain.CashInTransactions
	var err error
	// check status
	var cred snapbca.Credential
	cred, cashIn, err = s.getCredentialBcaSnap(ctx, cb.InvoiceNumber)
	if err != nil {
		logger.Error(ctx, err.Error())
		return
	}

	if cashIn.Status != constants.CashInStatusPending { // edge case
		logger.Error(ctx, "cash in status not pending", logger.Any("status", cashIn.Status))
		return
	}

	isNotInquiryYet := false
	lastReqId, err := s.snapRepo.GetLastVaRequestId(ctx, domain.SnapVaRequestId{
		CashInTransactionId: cashIn.ID,
	})
	if err != nil {
		logger.Error(ctx, err.Error())
		if err != gorm.ErrRecordNotFound { // means still not inquiry yet, skip
			return
		}
		logger.Info(ctx, fmt.Sprintf("cash in  %s still not inquiry yet", cashIn.InvoiceNumber))
		isNotInquiryYet = true
	}

	updateStatusOpts := domain.UpdateWachingStatusCashinOpts{}

	if cashIn.IsExpired() && isNotInquiryYet { // if cash in already expired and there is no inquiry
		logger.Info(ctx, fmt.Sprintf("cash in %s is expired", cashIn.InvoiceNumber))
		cb = cb.ToStatus(constants.CashInStatusExpired)
	} else if !cashIn.IsExpired() && isNotInquiryYet {
		logger.Info(ctx, fmt.Sprintf("[cashIn bca snap callback] cash in %s is not expired and still not inquiry yet", cashIn.InvoiceNumber))
		return // SKIP if cash in not expired and not inquiry yet
	} else { // if cash in already inquiry
		getStatusReq := snapbca.TransferVaStatusReq{}
		custNo := strings.Replace(lastReqId.VaNumber, cb.PartnerId, "", 1)
		getStatusReq.Build(cb.PartnerId, custNo, lastReqId.RequestId)
		resp, err := s.snapBcaRest.TransferVaStatus(ctx, getStatusReq, cred)
		if err != nil {
			logger.Error(ctx, err.Error())
			// return
		}
		cb.Request = getStatusReq
		cb.Response = resp
		if resp.VirtualAccountData.PaymentFlagStatus == "00" {
			cb = cb.ToStatus(constants.CashInStatusPaid)
			updateStatusOpts.PaymentAt = &resp.VirtualAccountData.TransactionDate
		} else if resp.VirtualAccountData.PaymentFlagStatus == "01" {
			cb = cb.ToStatus(constants.CashInStatusFailed)
		} else if cashIn.IsExpired() {
			cb = cb.ToStatus(constants.CashInStatusExpired)
			if resp.ResponseCode == "4042601" {
				logger.Info(ctx, fmt.Sprintf("[cashIn bca snap callback] cash in  %s not found in when get payment status and already expired", cashIn.InvoiceNumber))
				cb = cb.ToStatus(constants.CashInStatusFailed)
			}
		} else {
			logger.Warn(ctx, fmt.Sprintf("another case cash in %s , resp %+v", cashIn.InvoiceNumber, resp.VirtualAccountData))
			return // another case
		}
	}

	err = s.UpdateStatusSnapCashinTransaction(ctx, cashIn, cb, updateStatusOpts)
	if err != nil {
		logger.Error(ctx, err.Error())
		return
	}
}

func (s *defaultCashinTransaction) BatchingCashInToCashOut(ctx context.Context) (err error) {
	counter := 0
	for {
		err = s.batchingCashInToCashOutProcess(ctx)
		if err == nil {
			return
		}
		counter = counter + 1
		if err.Error() != http.StatusText(http.StatusInternalServerError) {
			return
		}

		time.Sleep(1 * time.Minute)
		if counter == 20 {
			err = errors.SetErrorMessage(http.StatusInternalServerError, "failed to bacthing cash in to cash out")
			logger.Error(ctx, err.Error())
			return
		}
	}
}

func (s *defaultCashinTransaction) RefundCashinTranscation(ctx context.Context, req entity.CashinRefundRequest, auth string) (resp *domain.CashInTransactions, err error) {
	company, err := s.companyService.ValidatorCompanySecret(ctx, auth)
	if err != nil {
		logger.Error(ctx, err.Error())
		return
	}

	cashIn, err := s.cashinRepo.GetCashinTransactionByInvoiceNumber(ctx, req.InvoiceNumber)
	if err != nil {
		err = errors.SetErrorMessage(http.StatusNotFound, "cash in transaction not found")
		logger.Error(ctx, err.Error())
		return
	}
	if cashIn.CompanyID != company.ID {
		err = errors.SetErrorMessage(http.StatusNotFound, "invalid company")
		logger.Error(ctx, err.Error())
		return
	}
	if cashIn.Status != constants.CashInStatusPaid {
		err = errors.SetErrorMessage(http.StatusNotFound, "invalid status "+cashIn.Status)
		logger.Error(ctx, err.Error())
		return
	}

	cashInItems, err := s.cashinRepo.GetCashinItems(ctx, cashIn.ID, true)
	if err != nil {
		err = errors.SetErrorMessage(http.StatusNotFound, "cash in items error")
		logger.Error(ctx, err.Error())
		return
	}
	if cashInItems != nil {
		err = errors.SetErrorMessage(http.StatusNotFound, "already in cashout process")
		logger.Error(ctx, err.Error())
		return
	}

	err = s.cashinRepo.UpdateStatusCashInTransaction(ctx, nil, cashIn.ID, constants.CashInStatusRefundRequest)
	if err != nil {
		logger.Error(ctx, err.Error())
		return
	}

	resp = cashIn
	resp.Status = constants.CashInStatusRefundRequest

	return
}

func (s *defaultCashinTransaction) RefundPaymentCashinTranscation(ctx context.Context, req entity.CashinBulkRequest) (err error) {
	var ids []int
	for _, v := range req.Data {
		ids = append(ids, v.ID)
	}
	cashIns, err := s.cashinRepo.GetCashinBulk(ctx, ids)
	if err != nil {
		err = errors.SetErrorMessage(http.StatusNotFound, "cash in transaction not found")
		logger.Error(ctx, err.Error())
		return
	}
	for _, v := range *cashIns {
		if v.Status != constants.CashInStatusRefundRequest {
			err = errors.SetErrorMessage(http.StatusNotFound, "invalid status "+v.Status)
			logger.Error(ctx, err.Error())
			return
		}
	}

	_, err = s.cashinRepo.UpdateStatusBulkCashInTranscation(ctx, nil, ids, constants.CashInStatusRefunded)
	if err != nil {
		logger.Error(ctx, err.Error())
		return
	}

	for _, v := range *cashIns {
		v.Status = constants.CashInStatusRefunded
		msq := messagebrokerMessage.Message{
			Type:    messagebrokerConfig.CONTENT_TYPE_JSON,
			Content: []byte(interfacepkg.Marshal(v)),
		}
		infrastructure.PublishMessage(strings.Replace(constants.CallbackClient, "*", fmt.Sprintf("#%s.*", strings.ToLower(v.CompanyCode)), 1), &msq)
	}

	return
}

//nolint:errcheck
func (s *defaultCashinTransaction) Export(ctx context.Context, req *domain.CashinExportReq) (filename string, err error) {
	//validate request
	dateNow := timepkg.DateNow()
	if req.StartDatetime.After(dateNow) || req.EndDatetime.After(dateNow) {
		err = errors.SetErrorMessage(http.StatusInternalServerError, "start date and end date cannot be greater than now")
		logger.Error(ctx, err.Error())
		return
	}

	// lastYear := dateNow.AddDate(-1, 0, 0)
	// if req.StartDatetime.Before(lastYear) {
	// 	err = errors.SetErrorMessage(http.StatusInternalServerError, "max start date is last year")
	// 	logger.Error(ctx, err.Error())
	// 	return
	// }

	data, err := s.cashinRepo.Export(ctx, req)
	if err != nil {
		err = errors.SetErrorMessage(http.StatusInternalServerError, "get data export error")
		logger.Error(ctx, err.Error())
		return
	}

	excelFile := excelize.NewFile()
	defer func() {
		if err := excelFile.Close(); err != nil {
			fmt.Println("excel file err", err)
		}
	}()

	cashInSheetName := "Cash In Export"
	excelFile.SetSheetName("Sheet1", cashInSheetName)
	// sheet, err := excelFile.NewSheet(cashInSheetName)
	// if err != nil {
	// 	err = errors.SetErrorMessage(http.StatusInternalServerError, "error create new sheet excel")
	// 	logger.Error(ctx, err.Error())
	// 	return
	// }

	boldStyle, err := excelFile.NewStyle(&excelize.Style{
		Font: &excelize.Font{
			Bold: true,
		},
		Alignment: &excelize.Alignment{Horizontal: "center", Vertical: "center"},
	})
	if err != nil {
		err = errors.SetErrorMessage(http.StatusInternalServerError, "error create bold style")
		logger.Error(ctx, err.Error())
		return
	}

	boldAndBorderStyle, err := excelFile.NewStyle(&excelize.Style{
		Font: &excelize.Font{
			Bold: true,
		},
		Alignment: &excelize.Alignment{Horizontal: "center", Vertical: "center"},
		Border: []excelize.Border{
			{Type: "top", Color: "#000000", Style: 1},
			{Type: "right", Color: "#000000", Style: 1},
			{Type: "left", Color: "#000000", Style: 1},
			{Type: "bottom", Color: "#000000", Style: 1},
		},
	})

	boldAndBorderStyle1, err := excelFile.NewStyle(&excelize.Style{
		Font: &excelize.Font{
			Bold: true,
		},
		Alignment: &excelize.Alignment{Horizontal: "center", Vertical: "center"},
		Border: []excelize.Border{
			{Type: "top", Color: "#000000", Style: 1},
			{Type: "right", Color: "#000000", Style: 1},
			{Type: "left", Color: "#000000", Style: 1},
		},
	})
	boldAndBorderStyle2, err := excelFile.NewStyle(&excelize.Style{
		Font: &excelize.Font{
			Bold: true,
		},
		Alignment: &excelize.Alignment{Horizontal: "center", Vertical: "center"},
		Border: []excelize.Border{
			{Type: "right", Color: "#000000", Style: 1},
			{Type: "left", Color: "#000000", Style: 1},
			{Type: "bottom", Color: "#000000", Style: 1},
		},
	})
	if err != nil {
		err = errors.SetErrorMessage(http.StatusInternalServerError, "error create bold and border style")
		logger.Error(ctx, err.Error())
		return
	}

	// set header
	excelFile.SetColWidth(cashInSheetName, "A", "S", 25)

	excelFile.SetCellValue(cashInSheetName, "A1", "Rekap Cash In report")
	excelFile.SetCellStyle(cashInSheetName, "A1", "A1", boldStyle)
	excelFile.MergeCell(cashInSheetName, "A1", "C1")

	excelFile.SetCellValue(cashInSheetName, "A2", fmt.Sprintf("Periode %s - %s", req.StartDatetime.Format("02/01/2006"), req.EndDatetime.Format("02/01/2006")))
	excelFile.SetCellStyle(cashInSheetName, "A2", "A2", boldStyle)
	excelFile.MergeCell(cashInSheetName, "A2", "C2")

	excelFile.SetCellValue(cashInSheetName, "A3", "Invoice Number")
	excelFile.SetCellStyle(cashInSheetName, "A3", "A3", boldAndBorderStyle1)
	excelFile.SetCellStyle(cashInSheetName, "A4", "A4", boldAndBorderStyle2)
	excelFile.MergeCell(cashInSheetName, "A3", "A4")

	excelFile.SetCellValue(cashInSheetName, "B3", "Customer Name")
	excelFile.SetCellStyle(cashInSheetName, "B3", "B3", boldAndBorderStyle1)
	excelFile.SetCellStyle(cashInSheetName, "B4", "B4", boldAndBorderStyle2)
	excelFile.MergeCell(cashInSheetName, "B3", "B4")

	excelFile.SetCellValue(cashInSheetName, "C3", "Product Name")
	excelFile.SetCellStyle(cashInSheetName, "C3", "C3", boldAndBorderStyle1)
	excelFile.SetCellStyle(cashInSheetName, "C4", "C4", boldAndBorderStyle2)
	excelFile.MergeCell(cashInSheetName, "C3", "C4")

	excelFile.SetCellValue(cashInSheetName, "D3", "Payment Provider")
	excelFile.SetCellStyle(cashInSheetName, "D3", "D3", boldAndBorderStyle1)
	excelFile.SetCellStyle(cashInSheetName, "D4", "D4", boldAndBorderStyle2)
	excelFile.MergeCell(cashInSheetName, "D3", "D4")

	excelFile.SetCellValue(cashInSheetName, "E3", "Payment Channel")
	excelFile.SetCellStyle(cashInSheetName, "E3", "E3", boldAndBorderStyle1)
	excelFile.SetCellStyle(cashInSheetName, "E4", "E4", boldAndBorderStyle2)
	excelFile.MergeCell(cashInSheetName, "E3", "E4")

	excelFile.SetCellValue(cashInSheetName, "F3", "VA Number")
	excelFile.SetCellStyle(cashInSheetName, "F3", "F3", boldAndBorderStyle1)
	excelFile.SetCellStyle(cashInSheetName, "F4", "F4", boldAndBorderStyle2)
	excelFile.MergeCell(cashInSheetName, "F3", "F4")

	excelFile.SetCellValue(cashInSheetName, "G3", "Subtotal Item")
	excelFile.SetCellStyle(cashInSheetName, "G3", "G3", boldAndBorderStyle1)
	excelFile.SetCellStyle(cashInSheetName, "G4", "G4", boldAndBorderStyle2)
	excelFile.MergeCell(cashInSheetName, "G3", "G4")

	excelFile.SetCellValue(cashInSheetName, "H3", "Total cash in")
	excelFile.SetCellStyle(cashInSheetName, "H3", "H3", boldAndBorderStyle1)
	excelFile.SetCellStyle(cashInSheetName, "H4", "H4", boldAndBorderStyle2)
	excelFile.MergeCell(cashInSheetName, "H3", "H4")

	excelFile.SetCellValue(cashInSheetName, "I3", "Delivery Fee")
	excelFile.SetCellStyle(cashInSheetName, "I3", "I3", boldAndBorderStyle1)
	excelFile.SetCellStyle(cashInSheetName, "I4", "I4", boldAndBorderStyle2)
	excelFile.MergeCell(cashInSheetName, "I3", "I4")

	excelFile.SetCellValue(cashInSheetName, "J3", "Transaction Fee")
	excelFile.SetCellStyle(cashInSheetName, "J3", "J3", boldAndBorderStyle1)
	excelFile.SetCellStyle(cashInSheetName, "J4", "J4", boldAndBorderStyle2)
	excelFile.MergeCell(cashInSheetName, "J3", "J4")

	excelFile.SetCellValue(cashInSheetName, "K3", "Discount")
	excelFile.SetCellStyle(cashInSheetName, "K3", "K3", boldAndBorderStyle1)
	excelFile.SetCellStyle(cashInSheetName, "K4", "K4", boldAndBorderStyle2)
	excelFile.MergeCell(cashInSheetName, "K3", "K4")

	excelFile.SetCellValue(cashInSheetName, "L3", "Cash In Fee")
	excelFile.SetCellStyle(cashInSheetName, "L3", "L3", boldAndBorderStyle1)
	excelFile.SetCellStyle(cashInSheetName, "L4", "L4", boldAndBorderStyle2)
	excelFile.MergeCell(cashInSheetName, "L3", "L4")

	excelFile.SetCellValue(cashInSheetName, "M3", "Product Fee") // cashout product fee
	excelFile.SetCellStyle(cashInSheetName, "M3", "M3", boldAndBorderStyle1)
	excelFile.SetCellStyle(cashInSheetName, "M4", "M4", boldAndBorderStyle2)
	excelFile.MergeCell(cashInSheetName, "M3", "M4")

	excelFile.SetCellValue(cashInSheetName, "N3", "Grand Total")
	excelFile.SetCellStyle(cashInSheetName, "N3", "N3", boldAndBorderStyle1)
	excelFile.SetCellStyle(cashInSheetName, "N4", "N4", boldAndBorderStyle2)
	excelFile.MergeCell(cashInSheetName, "N3", "N4")

	excelFile.SetCellValue(cashInSheetName, "O3", "Created Date")
	excelFile.SetCellStyle(cashInSheetName, "O3", "O3", boldAndBorderStyle1)
	excelFile.SetCellStyle(cashInSheetName, "O4", "O4", boldAndBorderStyle2)
	excelFile.MergeCell(cashInSheetName, "O3", "O4")

	excelFile.SetCellValue(cashInSheetName, "P3", "Settlement Date")
	excelFile.SetCellStyle(cashInSheetName, "P3", "P3", boldAndBorderStyle1)
	excelFile.SetCellStyle(cashInSheetName, "P4", "P4", boldAndBorderStyle2)
	excelFile.MergeCell(cashInSheetName, "P3", "P4")

	excelFile.SetCellValue(cashInSheetName, "Q3", "Cash In Status")
	excelFile.SetCellStyle(cashInSheetName, "Q3", "Q3", boldAndBorderStyle1)
	excelFile.SetCellStyle(cashInSheetName, "Q4", "Q4", boldAndBorderStyle2)
	excelFile.MergeCell(cashInSheetName, "Q3", "Q4")

	boldAndBorderStyle3, err := excelFile.NewStyle(&excelize.Style{
		Font: &excelize.Font{
			Bold: true,
		},
		Alignment: &excelize.Alignment{Horizontal: "center", Vertical: "center"},
		Border: []excelize.Border{
			{Type: "top", Color: "#000000", Style: 1},
			{Type: "left", Color: "#000000", Style: 1},
			{Type: "bottom", Color: "#000000", Style: 1},
		},
	})
	boldAndBorderStyle4, err := excelFile.NewStyle(&excelize.Style{
		Font: &excelize.Font{
			Bold: true,
		},
		Alignment: &excelize.Alignment{Horizontal: "center", Vertical: "center"},
		Border: []excelize.Border{
			{Type: "top", Color: "#000000", Style: 1},
			{Type: "bottom", Color: "#000000", Style: 1},
		},
	})
	boldAndBorderStyle5, err := excelFile.NewStyle(&excelize.Style{
		Font: &excelize.Font{
			Bold: true,
		},
		Alignment: &excelize.Alignment{Horizontal: "center", Vertical: "center"},
		Border: []excelize.Border{
			{Type: "top", Color: "#000000", Style: 1},
			{Type: "right", Color: "#000000", Style: 1},
			{Type: "bottom", Color: "#000000", Style: 1},
		},
	})
	excelFile.SetCellValue(cashInSheetName, "R3", "Jumlah Detail Items")
	excelFile.SetCellStyle(cashInSheetName, "S3", "P3", boldAndBorderStyle3)
	excelFile.SetCellStyle(cashInSheetName, "T3", "Q3", boldAndBorderStyle4)
	excelFile.SetCellStyle(cashInSheetName, "U3", "R3", boldAndBorderStyle4)
	excelFile.SetCellStyle(cashInSheetName, "V3", "S3", boldAndBorderStyle5)
	excelFile.MergeCell(cashInSheetName, "R3", "V3")

	excelFile.SetCellValue(cashInSheetName, "R4", "Partner Name")
	excelFile.SetCellStyle(cashInSheetName, "R4", "P4", boldAndBorderStyle)

	excelFile.SetCellValue(cashInSheetName, "S4", "Invoice Number (Cash in item)")
	excelFile.SetCellStyle(cashInSheetName, "S4", "R4", boldAndBorderStyle)

	excelFile.SetCellValue(cashInSheetName, "T4", "Total")
	excelFile.SetCellStyle(cashInSheetName, "T4", "S4", boldAndBorderStyle)

	excelFile.SetCellValue(cashInSheetName, "U4", "Note")
	excelFile.SetCellStyle(cashInSheetName, "U4", "T4", boldAndBorderStyle)

	excelFile.SetCellValue(cashInSheetName, "V4", "Status (Cash in Item)")
	excelFile.SetCellStyle(cashInSheetName, "V4", "U4", boldAndBorderStyle)

	rowIndex := 5
	mapCashIn := make(map[string]bool)
	for _, d := range data {
		if len(d.Items.Value()) > 0 {
			for _, item := range d.Items.Value() {
				rowIndexS := strconv.Itoa(rowIndex)
				_, exist := mapCashIn[d.InvoiceNumber]
				if !exist {
					excelFile.SetCellValue(cashInSheetName, "A"+rowIndexS, d.InvoiceNumber)
					excelFile.SetCellValue(cashInSheetName, "B"+rowIndexS, d.CustomerName)
					excelFile.SetCellValue(cashInSheetName, "C"+rowIndexS, d.CompanyProductCode)
					excelFile.SetCellValue(cashInSheetName, "D"+rowIndexS, d.PaymentProviderName)
					excelFile.SetCellValue(cashInSheetName, "E"+rowIndexS, d.PaymentChannelName)
					excelFile.SetCellValue(cashInSheetName, "F"+rowIndexS, d.VirtualAccount)
					excelFile.SetCellFloat(cashInSheetName, "G"+rowIndexS, d.CashInCalculation.GetSubTotalItem(), 0, 64)
					excelFile.SetCellFloat(cashInSheetName, "H"+rowIndexS, d.GetCashInTotal(), 0, 64)
					excelFile.SetCellFloat(cashInSheetName, "I"+rowIndexS, d.PgDeliveryFee, 0, 64)
					excelFile.SetCellFloat(cashInSheetName, "J"+rowIndexS, d.ProductFee, 0, 64)
					excelFile.SetCellFloat(cashInSheetName, "K"+rowIndexS, d.Voucher, 0, 64)
					excelFile.SetCellFloat(cashInSheetName, "L"+rowIndexS, d.AdminFee, 0, 64)
					excelFile.SetCellFloat(cashInSheetName, "M"+rowIndexS, d.CompanyProductFee, 0, 64)
					excelFile.SetCellFloat(cashInSheetName, "N"+rowIndexS, d.GetGrandTotal(), 0, 64)
					excelFile.SetCellValue(cashInSheetName, "O"+rowIndexS, d.CreatedAt.In(timepkg.WIB).Format(timepkg.TimeDateExportFormat))
					excelFile.SetCellValue(cashInSheetName, "P"+rowIndexS, d.PaymentAt.String(timepkg.TimeDateExportFormat, timepkg.WIB))
					excelFile.SetCellValue(cashInSheetName, "Q"+rowIndexS, d.Status)
					mapCashIn[d.InvoiceNumber] = true
				}
				// item
				excelFile.SetCellValue(cashInSheetName, "R"+rowIndexS, item.PartnerName)
				excelFile.SetCellValue(cashInSheetName, "S"+rowIndexS, item.RefInvoiceNumber)
				excelFile.SetCellFloat(cashInSheetName, "T"+rowIndexS, item.Amount, 0, 64)
				excelFile.SetCellValue(cashInSheetName, "U"+rowIndexS, item.Note)
				excelFile.SetCellValue(cashInSheetName, "V"+rowIndexS, item.Status)
				rowIndex++
			}
		}
	}

	filename = fmt.Sprintf("./static/%s_CashIn_%s_%s.xlsx", str.RandString(10), req.StartDatetime.Format("20060102"), req.EndDatetime.Format("20060102"))
	if err = excelFile.SaveAs(filename); err != nil {
		err = errors.SetErrorMessage(http.StatusInternalServerError, fmt.Sprintf("error save excel %v", err))
		logger.Error(ctx, err.Error())
		return
	}

	return
}

func (s *defaultCashinTransaction) CheckUnpaidBillVA(ctx context.Context, req *entity.CheckUnpaidBillVARequest, auth string) (res entity.CheckUnpaidBillVAResponse, err error) {
	company, err := s.companyService.ValidatorCompanySecret(ctx, auth)
	if err != nil {
		return
	}

	channelPayment, err := s.channelMappingRepo.GetChannelMappingByCode(ctx, req.PaymentMethodCode)
	if err != nil {
		err = errors.SetErrorMessage(http.StatusNotFound, "payment method not found")
		logger.Error(ctx, err.Error())
		return
	}
	if channelPayment.Capability != constants.CashInCapability {
		err = errors.SetErrorMessage(http.StatusBadRequest, "payment method does not support cash in transactions")
		logger.Error(ctx, err.Error())
		return
	}

	companyId := strconv.Itoa(company.ID)
	providerId := strconv.Itoa(int(channelPayment.PaymentProviderID))
	channelId := strconv.Itoa(int(channelPayment.PaymentChannelID))
	cashInCapability := strconv.Itoa(constants.CashInCapability)

	providerMapping, err := s.companyMappingService.GetCompanyProviderMappingByCompanyIdAndProviderId(ctx, companyId, providerId)
	if err != nil {
		return
	}
	if !providerMapping.Status {
		err = errors.SetErrorMessage(http.StatusBadRequest, "company payment provider is not active")
		logger.Error(ctx, err.Error())
		return
	}

	companyMapping, err := s.companyMappingRepo.GetCompanyProviderChannelMappingRequestBy(ctx, companyId, providerId, channelId, cashInCapability)
	if err != nil {
		err = errors.SetErrorMessage(http.StatusNotFound, "payment method not found in company")
		logger.Error(ctx, err.Error())
		return
	}

	fvaCust, err := s.companyMappingService.GetOneCustomerFVAByPhone(ctx, req.CustomerPhone, companyMapping.ID)
	if err != nil {
		if errors.GetErrorCode(err) == http.StatusNotFound {
			res = entity.CheckUnpaidBillVAResponse{
				IsExist: false,
			}
			return res, nil
		}
		return
	}

	if len(fvaCust.LastPgReferenceId) <= 0 {
		res = entity.CheckUnpaidBillVAResponse{
			IsExist: false,
		}
		return res, nil
	}

	providerResp, err := s.providerService.GetPaymentProviderById(ctx, providerId)
	if err != nil {
		return
	}
	if !providerResp.Status {
		err = errors.SetErrorMessage(http.StatusBadRequest, "payment provider status is not active")
		logger.Error(ctx, err.Error())
		return
	}

	switch strings.ToLower(providerResp.Name) {
	case constants.ProviderXendit:
		credential, err := xendit.GetXenditCredential(providerMapping.ProviderSecrets)
		if err != nil {
			err = errors.SetErrorMessage(http.StatusInternalServerError, err.Error())
			logger.Error(ctx, err.Error())
			return res, err
		}

		var xenditExistVA xendit.VaResponse
		xenditExistVA, err = s.xenditRest.CheckStatusPaymentVA(ctx, fvaCust.LastPgReferenceId, credential)
		errCode := errors.GetErrorCode(err)
		if err != nil && errCode != http.StatusNotFound {
			err = errors.SetErrorMessage(http.StatusInternalServerError, fmt.Sprintf("error when get latest fixed va transaction %v", err.Error()))
			logger.Error(ctx, err.Error())
			return res, err
		}

		if xenditExistVA.Status == constants.XenditVAStatusActive {
			res.IsExist = true
		}
	case constants.ProviderBankBCA:
		cashIn, err := s.cashinRepo.GetCashinTransactionByInvoiceNumber(ctx, fvaCust.LastPgReferenceId)
		if err != nil && err != gorm.ErrRecordNotFound {
			err = errors.SetErrorMessage(http.StatusInternalServerError, fmt.Sprintf("error when get latest va transaction %v", err.Error()))
			logger.Error(ctx, err.Error())
			return res, err
		}

		if cashIn.PaymentStatus == constants.PaymentDraft {
			res.IsExist = true
		}
	default:
		err = errors.SetErrorMessage(http.StatusForbidden, "invalid payment provider")
		logger.Error(ctx, err.Error())
		return
	}

	return
}

func (s *defaultCashinTransaction) GetPaymentInstructions(ctx context.Context, invNumber, auth string) (res entity.PaymentInstructionResp, err error) {
	cashin, err := s.cashinRepo.GetCashinTransactionByInvoiceNumber(ctx, invNumber)
	if err != nil {
		logger.Error(ctx, err.Error())
		return
	}
	channel, err := s.channelMappingRepo.GetChannelMappingByProviderChannelIdAndCapability(ctx, fmt.Sprintf("%d", cashin.PaymentProviderID), fmt.Sprintf("%d", cashin.PaymentChannelID), fmt.Sprintf("%d", constants.CashInCapability))
	if err != nil {
		logger.Error(ctx, err.Error())
		return
	}
	res.Instructions = channel.PaymentInstructions
	return
}
