package repositoty

import (
	"context"
	"time"

	"gorm.io/gorm"
	"gorm.io/gorm/clause"

	"repo.nusatek.id/nusatek/payment/domain"
	"repo.nusatek.id/nusatek/payment/modules/cash_in_transaction/entity"
	"repo.nusatek.id/nusatek/payment/modules/cash_in_transaction/usecase"
	"repo.nusatek.id/nusatek/payment/utils"
	"repo.nusatek.id/nusatek/payment/utils/constants"
	"repo.nusatek.id/nusatek/payment/utils/timepkg"
)

type defautCashinTransaction struct {
	db *gorm.DB
}

func NewRepository(db *gorm.DB) usecase.CashinTranscationRepo {
	return &defautCashinTransaction{db}
}

func (r *defautCashinTransaction) BeginTrans() *gorm.DB {
	return r.db.Begin()
}

func (r *defautCashinTransaction) CreateCashinTransaction(ctx context.Context, tx *gorm.DB, req *domain.CashInTransactions) (err error) {
	err = tx.WithContext(ctx).Create(req).Error
	return
}

func (r *defautCashinTransaction) UpdateCashinTransaction(ctx context.Context, tx *gorm.DB, req *domain.CashInTransactions) (err error) {
	err = tx.WithContext(ctx).Save(req).Error
	return
}

func (r *defautCashinTransaction) UpdateCashinTransactionHistory(ctx context.Context, tx *gorm.DB, req *domain.CashInTransactionHistories) (err error) {
	err = tx.WithContext(ctx).Save(req).Error
	return
}

func (r *defautCashinTransaction) UpdateCashinTransactionHistoryFromCallback(ctx context.Context, req *domain.CashInTransactionHistories, cashinTransactionId, providerId, channelId int) (err error) {
	err = r.db.WithContext(ctx).Where("cash_in_transaction_id = ? AND channel_id = ? AND provider_id = ?", cashinTransactionId, channelId, providerId).Updates(req).Error
	return
}

func (r *defautCashinTransaction) GetCashinTransactionHistoryFromCallback(ctx context.Context, cashinTransactionId, providerId, channelId int) (resp *domain.CashInTransactionHistories, err error) {
	err = r.db.WithContext(ctx).Where("cash_in_transaction_id = ? AND channel_id = ? AND provider_id = ?", cashinTransactionId, channelId, providerId).Order("id DESC").Take(&resp).Error
	return
}

func (r *defautCashinTransaction) GetCashinTransactionHistoryByPaymentStatus(ctx context.Context, cashinTransactionId int, paymentStatus string) (resp *domain.CashInTransactionHistories, err error) {
	err = r.db.WithContext(ctx).Where("cash_in_transaction_id = ? AND payment_status = ? ", cashinTransactionId, paymentStatus).Order("id DESC").Take(&resp).Error
	return
}

func (r *defautCashinTransaction) GetCashinTransactionById(ctx context.Context, id string) (resp *domain.CashInTransactions, err error) {
	err = r.db.WithContext(ctx).First(&resp, id).Error
	return
}

func (r *defautCashinTransaction) GetCashinTransactionByIdDetail(ctx context.Context, id string) (resp domain.CashInTransactionDetail, err error) {
	err = r.db.Debug().WithContext(ctx).Table("cash_in_transactions").Select(`cash_in_transactions.id, cash_in_transactions.company_id, cash_in_transactions.company_product_id, 
	cash_in_transactions.invoice_number, cash_in_transactions.payment_at, cash_in_transactions.manual_payment_at, cash_in_transactions.customer_name, 
	cash_in_transactions.customer_phone, cash_in_transactions.customer_email, cash_in_transactions.payment_provider_id, cash_in_transactions.payment_channel_id, 
	cash_in_transactions.total, cash_in_transactions.admin_fee, 
	cash_in_transactions.discount, cash_in_transactions.voucher, cash_in_transactions.pg_delivery_fee, cash_in_transactions.payment_status, cash_in_transactions.status, cash_in_transactions.created_at, cash_in_transactions.updated_at, 
	cash_in_transactions.deleted_at, cash_in_transactions.product_fee, cash_in_transactions.expired_at, cash_in_transactions.company_product_fee,	
	payment_providers."name" payment_provider_name,
	payment_channels."name" payment_channel_name,
	payment_channel_types."payment_type" payment_channel_type_payment_type,
	company_products.cashout_fee_fix_value "company_product_cashout_fee_fix_value",
	company_products.cashout_fee_percentage "company_product_cashout_fee_percentage",
	c."name" as company_name,
	p.product_name
	`).
		Joins(`LEFT JOIN payment_providers ON payment_providers.id = cash_in_transactions.payment_provider_id`).
		Joins(`LEFT JOIN payment_channels ON payment_channels.id = cash_in_transactions.payment_channel_id`).
		Joins(`LEFT JOIN payment_channel_types ON payment_channel_types.id = payment_channels.payment_channel_type_id`).
		Joins(`INNER JOIN company_products ON company_products.id = cash_in_transactions.company_product_id`).
		Joins(`INNER JOIN companies c ON cash_in_transactions.company_id = c.id`).
		Joins(`INNER JOIN company_products p ON cash_in_transactions.company_product_id = p.id `).
		Where(`cash_in_transactions.id = ?`, id).Take(&resp).Error

	// resp.PartnerFee = (resp.PartnerFeePercentage / 100 * resp.Total) + resp.PartnerFeeFixValue

	return
}

func (r *defautCashinTransaction) GetCashinTransactionByInvoiceNumber(ctx context.Context, invoiceNumber string) (resp *domain.CashInTransactions, err error) {
	err = r.db.WithContext(ctx).Where("invoice_number = ?", invoiceNumber).First(&resp).Error
	return
}

func (r *defautCashinTransaction) GetCashinBulk(ctx context.Context, ids []int) (resp *[]domain.CashInTransactions, err error) {
	err = r.db.WithContext(ctx).Select(`cash_in_transactions.*, companies.code AS company_code,
		companies.name AS company_name`).
		Joins(`LEFT JOIN companies ON companies.id = cash_in_transactions.company_id`).
		Where("cash_in_transactions.id IN ?", ids).Find(&resp).Error
	return
}

func (r *defautCashinTransaction) GetCashinByStatus(ctx context.Context, status string) (resp *[]domain.CashInTransactions, err error) {
	err = r.db.WithContext(ctx).Select(`cash_in_transactions.*, payment_providers.name AS payment_provider_name,
		payment_channel_types.payment_type AS payment_channel_type,
		(SELECT json_agg(pg_reference_information ORDER BY created_at desc) FROM cash_in_transaction_histories WHERE cash_in_transactions.id = cash_in_transaction_histories.cash_in_transaction_id) ->> 0 as pg_reference_information`).
		Joins(`LEFT JOIN payment_providers ON payment_providers.id = cash_in_transactions.payment_provider_id`).
		Joins(`LEFT JOIN payment_channels ON payment_channels.id = cash_in_transactions.payment_channel_id`).
		Joins(`LEFT JOIN payment_channel_types ON payment_channel_types.id = payment_channels.payment_channel_type_id`).
		Joins(`LEFT JOIN cash_in_transaction_histories ON cash_in_transaction_histories.cash_in_transaction_id = cash_in_transactions.id
		AND cash_in_transaction_histories.status = 'pending' AND cash_in_transaction_histories.deleted_at IS NULL`).
		Where("cash_in_transactions.status = ?", status).Find(&resp).Error

	return
}

func (r *defautCashinTransaction) DeleteCashinTranscation(ctx context.Context, req *domain.CashInTransactions) (err error) {
	err = r.db.WithContext(ctx).Delete(req).Error
	return
}

func (r *defautCashinTransaction) UpdateCashinTransactionItems(ctx context.Context, req *domain.CashInTransactionItems, tx *gorm.DB) (err error) {
	err = tx.WithContext(ctx).Save(req).Error
	return
}

func (r *defautCashinTransaction) GetCompanyMappingCashIn(ctx context.Context, companyId, providerId, channelId string) (resp *domain.CompanyPaymentProviderChannelMappings, err error) {
	err = r.db.WithContext(ctx).Model(domain.CompanyPaymentProviderChannelMappings{}).
		Where("company_id = ? AND payment_provider_id = ? AND payment_channel_id = ? AND capability = 1", companyId, providerId, channelId).First(&resp).Error
	return
}

func (r *defautCashinTransaction) GetCashinTranscationList(ctx context.Context, param entity.CashinListParam) (resp *[]entity.CashinList, totalData int64, err error) {
	query := func(db *gorm.DB) *gorm.DB {
		condision := db.Where("c.deleted_at IS NULL AND t.deleted_at IS NULL")

		if param.Pagination.FromDate != "" && param.Pagination.ToDate != "" {
			fromDate := param.Pagination.FromDate + " 00:00:00.000"
			toDate := param.Pagination.ToDate + " 23:59:59.000"
			condision.Where("t.created_at BETWEEN ? AND ?", fromDate, toDate)
		}

		if len(param.Statuses) > 0 {
			condision.Where("t.status IN ?", param.Statuses)
		}

		if len(param.PaymentChannelIDs) > 0 {
			condision.Where("t.payment_channel_id IN ?", param.PaymentChannelIDs)
		}

		if len(param.PaymentProviderIDs) > 0 {
			condision.Where("t.payment_provider_id IN ?", param.PaymentProviderIDs)
		}

		if len(param.CompanyProductIDs) > 0 {
			condision.Where("t.company_product_id IN ?", param.CompanyProductIDs)
		}

		if param.Pagination.Value != "" {
			switch true {
			case param.Pagination.Key == "company_name":
				condision.Where("c.\"name\" ILIKE ?", "%"+param.Pagination.Value+"%")

			case param.Pagination.Key == "payment_channel":
				condision.Where("s.\"name\" ILIKE ?", "%"+param.Pagination.Value+"%")

			case param.Pagination.Key == "invoice_number":
				condision.Where("t.invoice_number ILIKE ?", "%"+param.Pagination.Value+"%")

			case param.Pagination.Key == "customer_name":
				condision.Where("t.customer_name ILIKE ?", "%"+param.Pagination.Value+"%")

			case param.Pagination.Key == "product_name":
				condision.Where("p.product_name ILIKE ?", "%"+param.Pagination.Value+"%")

			case param.Pagination.Key == "total_amount":
				condision.Where("t.total = ?", param.Pagination.Value)

			case param.Pagination.Key == "discount":
				condision.Where("t.discount = ?", param.Pagination.Value)

			case param.Pagination.Key == "payment_provider":
				condision.Where("m.\"name\" ILIKE ?", "%"+param.Pagination.Value+"%")

			case param.Pagination.Key == "payment_status":
				condision.Where("t.status = ?", param.Pagination.Value)

			case param.Pagination.Key == "created_date":
				condision.Where("t.created_at = ?", param.Pagination.Value)

			case param.Pagination.Key == "settlement_date":
				condision.Where("t.payment_at = ?", param.Pagination.Value)

			case param.Pagination.Key == "company_id":
				condision.Where("c.id = ?", param.Pagination.Value)

			case param.Pagination.Key == "cash_in_id":
				condision.Where("t.id = ?", param.Pagination.Value)

			default:
				searchValue := "%" + param.Pagination.Value + "%"
				condision.Where(`c."name" ILIKE ? OR s."name" ILIKE ? OR t.invoice_number ILIKE ? OR t.customer_name ILIKE ? OR p.product_name ILIKE ?`, searchValue, searchValue, searchValue, searchValue, searchValue)
			}
		}

		return condision
	}
	q := r.db.Debug().WithContext(ctx).Table("cash_in_transactions t").
		Select(`t.id, c."name" as company_name, p.product_name, t.invoice_number, t.customer_name, t.customer_phone, t.total as amount, m."name" as payment_provider, 
		s."name" as payment_channel, t.admin_fee, t.created_at as create_date, t.payment_at as settlement_date, t.status as payment_status, 
		t.pg_delivery_fee, t.company_product_id, c.id as company_id, t.product_fee, t.company_product_fee,
		t.voucher, 
		p.cashout_fee_fix_value "company_product_cashout_fee_fix_value", 
		p.cashout_fee_percentage "company_product_cashout_fee_percentage",
		fl.url "logo_url"`).
		Joins(`INNER JOIN companies c ON t.company_id = c.id`).
		Joins(`INNER JOIN company_products p ON t.company_product_id = p.id `).
		Joins(`INNER JOIN payment_providers m ON t.payment_provider_id = m.id `).
		Joins(`INNER JOIN payment_channels s ON t.payment_channel_id = s.id`).
		Joins(`INNER JOIN files fl ON fl.id = s.logo_id`).
		Scopes(query)

	// count first
	err = q.Count(&totalData).Error
	if err != nil {
		return
	}

	err = q.Scopes(utils.Paginate(param.Pagination.Page, param.Pagination.Limit)).Order(param.Pagination.GetOrderBy("t")).Find(&resp).Error
	if err != nil {
		return
	}

	return
}

func (r *defautCashinTransaction) GetListTranscationHistoryByCashInTransactionID(ctx context.Context, paginate utils.Pagination, id string) (resp *[]entity.CashInTransactionHistories, totalData int64, err error) {
	joinQuery := "JOIN cash_in_transactions t ON h.cash_in_transaction_id = t.id"
	whereQuery := "h.cash_in_transaction_id = ? AND h.deleted_at IS NULL"

	err = r.db.WithContext(ctx).Scopes(utils.Paginate(paginate.Page, paginate.Limit)).Table("cash_in_transaction_histories h").
		Select("h.id, h.cash_in_transaction_id, h.description, h.pg_reference_information, h.payment_status, h.status, h.channel_id, h.provider_id, h.virtual_account, h.created_at").
		Joins(joinQuery).Where(whereQuery, id).Order(paginate.GetOrderBy("h")).Find(&resp).Error
	if err != nil {
		return
	}
	err = r.db.WithContext(ctx).Table("cash_in_transaction_histories h").Joins(joinQuery).Where(whereQuery, id).Count(&totalData).Error
	return
}

func (r *defautCashinTransaction) GetListItemsByTransactionID(ctx context.Context, p utils.Pagination, transactionID int64) (resp []domain.CashInTransactionItemComplete, totalData int64, err error) {
	whereScopes := func(db *gorm.DB) *gorm.DB {
		scopes := db.Where("cash_in_transaction_items.cash_in_transaction_id = ?", transactionID)

		return scopes
	}

	q := r.db.WithContext(ctx).Debug().Table("cash_in_transaction_items").Select(`
	cash_in_transaction_items.id,
	cash_in_transaction_items.cash_in_transaction_id,
	cash_in_transaction_items.partner_id,
	partners.name "partner_name",
	cash_in_transaction_items.ref_invoice_number,
	cash_in_transaction_items.item_name,
	cash_in_transaction_items.amount,
	cash_in_transaction_items.status,
	cash_in_transaction_items.note,
	cash_in_transaction_items.is_cashout,
	cash_in_transaction_items.sla_date,
	cash_in_transaction_items.created_at,
	cash_in_transaction_items.updated_at,
	cash_out_transaction_details.created_at "reconciled_date",
	cash_out_transaction_details.id IS NOT NULL "is_outstanding_cash_out",
	cash_out_transactions.payment_at "disbursement_date",
	cash_out_transactions.status "disbursement_status",
	cash_out_transactions.batch_number "batch_number_cash_out"`).
		Joins(`LEFT JOIN partners ON partners.id = cash_in_transaction_items.partner_id`).
		Joins(`LEFT JOIN cash_out_transaction_details ON cash_out_transaction_details.cash_in_transaction_item_id = cash_in_transaction_items.id AND cash_out_transaction_details.deleted_at IS NULL`).
		Joins(`LEFT JOIN cash_out_transactions ON cash_out_transactions.id = cash_out_transaction_details.cash_out_transaction_id AND cash_out_transactions.deleted_at IS NULL`).
		Scopes(whereScopes)

	if p.IsValidParam() {
		err = q.Count(&totalData).Error
		if err != nil {
			return
		}
	}

	err = q.Scopes(utils.Paginate(p.Page, p.Limit)).Order(p.GetOrderByMaps("", make(map[string]string))).Find(&resp).Error
	if err != nil {
		return
	}

	return
}

func (r *defautCashinTransaction) CreateCashinTransactionHistory(ctx context.Context, tx *gorm.DB, req *domain.CashInTransactionHistories) (err error) {
	if tx == nil {
		tx = r.db
	}
	err = tx.WithContext(ctx).Create(req).Error
	return
}

func (r *defautCashinTransaction) CreateBulkCashinTransactionHistory(ctx context.Context, tx *gorm.DB, req []domain.CashInTransactionHistories) (err error) {
	if tx == nil {
		tx = r.db
	}
	err = tx.WithContext(ctx).Create(req).Error
	return
}

func (r *defautCashinTransaction) UpdateStatusCashOutTranscation(ctx context.Context, id int, status string) (err error) {
	req := domain.CashOutTransactions{
		ID:     id,
		Status: status,
	}
	err = r.db.WithContext(ctx).Updates(req).Error
	return
}

func (r *defautCashinTransaction) UpdateStatusCashInTransaction(ctx context.Context, tx *gorm.DB, id int, status string) (err error) {
	req := domain.CashInTransactions{
		ID:     id,
		Status: status,
	}
	if tx == nil {
		tx = r.db
	}
	err = tx.WithContext(ctx).Updates(req).Error
	return
}

func (r *defautCashinTransaction) CreateCashOutTranscationHistory(ctx context.Context, req *domain.CashOutTransactionHistories) (err error) {
	err = r.db.WithContext(ctx).Create(req).Error
	return
}

func (r *defautCashinTransaction) UpdateStatusCashinTransactionItemsByCashinTranscationId(ctx context.Context, id int, status string) (err error) {
	req := domain.CashInTransactionItems{
		Status: status,
	}
	err = r.db.WithContext(ctx).Where("cash_in_transaction_id = ?", id).Updates(req).Error
	return
}

func (s *defautCashinTransaction) GetCashinTranscationItemByCashinId(ctx context.Context, id string) (resp *[]domain.CashInTransactionItems, err error) {
	err = s.db.WithContext(ctx).Where("cash_in_transaction_id = ?", id).Find(&resp).Error
	return
}

func (s *defautCashinTransaction) GetCashinTransactionItemsById(ctx context.Context, id string) (resp *domain.CashInTransactionItems, err error) {
	err = s.db.WithContext(ctx).First(&resp, id).Error
	return
}

func (s *defautCashinTransaction) GetCashinTransactionItemsByIds(ctx context.Context, ids []int) (resp []domain.CashInTransactionItems, err error) {
	err = s.db.WithContext(ctx).Where("id IN ?", ids).Find(&resp).Error
	return
}

func (s *defautCashinTransaction) GetCashinTransactionItemsByIdsWithCashIn(ctx context.Context, ids []int) (resp []domain.CashInTransactionItemsWithCashIn, err error) {
	err = s.db.WithContext(ctx).Model(domain.CashInTransactionItems{}).
		Select(`cash_in_transaction_items.*, cash_in_transactions.invoice_number "cash_in_invoice_number"`).
		Joins(`JOIN cash_in_transactions ON cash_in_transactions.id = cash_in_transaction_items.cash_in_transaction_id`).
		Where("cash_in_transaction_items.id IN ?", ids).Find(&resp).Error
	return
}

func (r *defautCashinTransaction) GetCashOutTransactionDetailsByCashinTranscationItemId(ctx context.Context, id string) (resp *domain.CashOutTransactionDetails, err error) {
	err = r.db.WithContext(ctx).Model(domain.CashOutTransactionDetails{}).Where("cash_in_transaction_item_id = ?", id).Take(&resp).Error
	return
}

func (r *defautCashinTransaction) CreateCashOutTransaction(ctx context.Context, req *domain.CashOutTransactions, tx *gorm.DB) (err error) {
	err = tx.WithContext(ctx).Create(req).Error
	return
}

func (r *defautCashinTransaction) CreateCashOutTransactionDetail(ctx context.Context, req *domain.CashOutTransactionDetails, tx *gorm.DB) (err error) {
	err = tx.WithContext(ctx).Create(req).Error
	return
}

func (r *defautCashinTransaction) CreateBulkCashOutTransactionDetail(ctx context.Context, req *[]domain.CashOutTransactionDetails, tx *gorm.DB) (err error) {
	if tx == nil {
		tx = r.db
	}
	err = tx.WithContext(ctx).Create(req).Error
	return
}

func (r *defautCashinTransaction) DeleteCashoutTranscationDetail(ctx context.Context, cashoutId, itemId int, tx *gorm.DB) (err error) {
	err = tx.WithContext(ctx).Where("cash_out_transaction_id = ? AND cash_in_transaction_item_id = ?", cashoutId, itemId).Delete(&domain.CashOutTransactionDetails{}).Error
	return
}

func (r *defautCashinTransaction) CreateCompanyCashFlow(ctx context.Context, req *domain.CompanyCashFlows, tx *gorm.DB) (err error) {
	err = tx.WithContext(ctx).Create(req).Error
	return
}

func (r *defautCashinTransaction) CreateCompanyCashFlowBulk(ctx context.Context, req []domain.CompanyCashFlows, tx *gorm.DB) (err error) {
	if tx == nil {
		tx = r.db
	}
	err = tx.WithContext(ctx).Create(req).Error
	return
}

func (r *defautCashinTransaction) UpdateCompanyCashFlowByCashOutId(ctx context.Context, req *domain.CompanyCashFlows) (err error) {
	err = r.db.WithContext(ctx).Where("cash_out_transaction_id = ?").Updates(req).Error
	return
}

func (r *defautCashinTransaction) FilterCashInItemsByDate(ctx context.Context, date string) (resp *[]domain.CashInTransactionItems, err error) {
	from := date + " 00:00:00.000"
	to := date + " 23:59:59.000"
	err = r.db.WithContext(ctx).Where("sla_date BETWEEN ? AND ? AND is_cashout = FALSE", from, to).Find(&resp).Error
	return
}

func (r *defautCashinTransaction) FilterUnforwardedCashInItems(ctx context.Context) (resp []domain.CashInTransactionItemsCashout, err error) {
	// err = r.db.WithContext(ctx).Where("sla_date IS NOT NULL AND sla_date <= NOW() AND is_cashout = FALSE").Find(&resp).Error
	// only check the date ignore the time
	// err = r.db.WithContext(ctx).Where("sla_date IS NOT NULL AND DATE_TRUNC('day',sla_date AT TIME ZONE 'Asia/Jakarta') <= DATE_TRUNC('day',NOW() AT TIME ZONE 'Asia/Jakarta') AND is_cashout = FALSE").Find(&resp).Error
	err = r.db.WithContext(ctx).Select(`cit.*, ci.invoice_number "cash_in_invoice_number", ci.company_product_id "cash_in_transaction_company_product_id"`).
		Table("cash_in_transaction_items cit").
		Joins("JOIN cash_in_transactions ci ON cit.cash_in_transaction_id = ci.id AND ci.deleted_at IS NULL").
		Where("cit.deleted_at is NULL AND cit.sla_date IS NOT NULL AND DATE_TRUNC('day',cit.sla_date AT TIME ZONE 'Asia/Jakarta') <= DATE_TRUNC('day',NOW() AT TIME ZONE 'Asia/Jakarta') AND cit.is_cashout = FALSE").Scan(&resp).Error
	return
}

// get outstanding cashin items
// the query is same as GetAllOutstandingItemCashIn in file modules/cash_out_transaction/repository/repo.go
func (r *defautCashinTransaction) FilterUnforwardedOutstandingCashInItems(ctx context.Context) (resp []domain.CashInTransactionItemsCashout, err error) {
	err = r.db.Debug().WithContext(ctx).Table("cash_in_transactions cit").
		Select(`citi.*, cit.company_product_id "cash_in_transaction_company_product_id"`).
		Joins(`JOIN payment_channels pc ON pc.id = cit.payment_channel_id`).
		Joins(`JOIN payment_providers pp ON pp.id = cit.payment_provider_id `).
		Joins(`JOIN cash_in_transaction_items citi ON citi.cash_in_transaction_id = cit.id`).
		Joins(`JOIN partners p ON citi.partner_id = p.id`).
		Joins(`LEFT JOIN cash_out_transaction_details co ON co.cash_in_transaction_item_id = citi.id AND co.deleted_at IS NULL`).
		Where("co.id IS NULL AND citi.is_cashout = true"). // outstanding condition
		Where("citi.deleted_at is NULL AND citi.sla_date IS NOT NULL AND DATE_TRUNC('day',citi.sla_date AT TIME ZONE 'Asia/Jakarta') <= DATE_TRUNC('day',NOW() AT TIME ZONE 'Asia/Jakarta')").Scan(&resp).Error

	return
}

func (r *defautCashinTransaction) GetLastCashOutTransactions(ctx context.Context) (resp *domain.CashOutTransactions, err error) {
	err = r.db.WithContext(ctx).Raw("SELECT * FROM cash_out_transactions ORDER BY id DESC LIMIT 1").Scan(&resp).Error
	return
}

func (r *defautCashinTransaction) SelectCashOutByPartnerIds(ctx context.Context, partnerIds []int, status string) (resp []domain.CashOutTransactions, err error) {
	err = r.db.Where("deleted_at IS NULL AND partner_id IN ? AND status = ?", partnerIds, status).Table("cash_out_transactions").
		Order("created_at").Scan(&resp).Error

	return resp, err
}

func (r *defautCashinTransaction) GetCashInByCashoutID(ctx context.Context, cashoutID int) (resp []domain.CashInTransactionItems, err error) {
	err = r.db.Select(`ci_item.*`).Debug().
		Table(`cash_out_transaction_details co_detail`).
		Joins(`INNER JOIN cash_in_transaction_items ci_item ON ci_item.id = co_detail.cash_in_transaction_item_id AND ci_item.deleted_at IS NULL`).
		Where("co_detail.deleted_at IS null AND co_detail.cash_out_transaction_id = ?", cashoutID).
		Order("co_detail.created_at ASC").Scan(&resp).Error

	return resp, err
}

func (r *defautCashinTransaction) UpdateIsCashoutBulk(ctx context.Context, tx *gorm.DB, ids []int) (err error) {
	err = tx.WithContext(ctx).Exec("UPDATE cash_in_transaction_items SET is_cashout = TRUE WHERE id IN ?", ids).Error

	return err
}

func (r *defautCashinTransaction) UpdateIsCashoutAndStatusBulk(ctx context.Context, tx *gorm.DB, ids []int, status string) (err error) {
	err = tx.WithContext(ctx).Exec("UPDATE cash_in_transaction_items SET is_cashout = TRUE, status =  ? WHERE id IN ?", status, ids).Error

	return err
}

func (r *defautCashinTransaction) UpdateItemStatusBulk(ctx context.Context, tx *gorm.DB, ids []int, status string) (err error) {
	if tx == nil {
		tx = r.db
	}
	err = tx.WithContext(ctx).Exec("UPDATE cash_in_transaction_items SET status =  ? WHERE id IN ?", status, ids).Error

	return err
}

func (r *defautCashinTransaction) UpdatePartialCashoutTransaction(ctx context.Context, tx *gorm.DB, req *domain.CashOutTransactions) (err error) {
	err = tx.WithContext(ctx).Updates(req).Error
	return
}

func (r *defautCashinTransaction) GetCashinItems(ctx context.Context, cashinID int, isCashout bool) (resp *[]domain.CashInTransactionItems, err error) {
	err = r.db.WithContext(ctx).Raw(`SELECT * FROM cash_in_transaction_items WHERE deleted_at IS NULL
		AND cash_in_transaction_id = ? AND is_cashout = ?`, cashinID, isCashout).Scan(&resp).Error
	return
}

func (r *defautCashinTransaction) UpdateStatusBulkCashInTranscation(ctx context.Context, tx *gorm.DB, ids []int, status string) (res []domain.CashInTransactions, err error) {
	if tx == nil {
		tx = r.db
	}
	err = tx.Debug().Model(&res).Clauses(clause.Returning{}).Where("id IN ?", ids).Update("status", status).Error

	return
}

func (r *defautCashinTransaction) GetAllByIDsWithItemCount(ctx context.Context, tx *gorm.DB, cashInIDs []int) (res []domain.CashInTransactionWithItemCount, err error) {
	if tx == nil {
		tx = r.db
	}
	q := tx.Debug().Table("cash_in_transactions c").Select(`c.id, c.invoice_number, c.status, c.payment_status, c.payment_provider_id, c.payment_channel_id, 
	COUNT(ci.id) item_count, string_agg(DISTINCT ci.status,',') item_statuses`).
		Joins(`LEFT JOIN cash_in_transaction_items ci ON ci.cash_in_transaction_id = c.id AND ci.deleted_at IS NULL`).
		Where("cash_in_transaction_id IN ?", cashInIDs).Group("c.id")

	err = q.Find(&res).Error
	return
}

func (r *defautCashinTransaction) Export(ctx context.Context, req *domain.CashinExportReq) (res []domain.CashinExportRes, err error) {
	sd := req.StartDatetime
	startDate := time.Date(sd.Year(), sd.Month(), sd.Day(), 0, 0, 0, 0, sd.Location())
	ed := req.EndDatetime
	endDate := time.Date(ed.Year(), ed.Month(), ed.Day()+1, 0, 0, 0, 0, ed.Location())

	q := r.db.Debug().Table("cash_in_transactions ci").
		Select(`ci.invoice_number, ci.customer_name, cp.code "company_product_code", 
		p_prov."name" payment_provider_name, p_chan."name" payment_channel_name,
		ci.total, ci.voucher, ci.discount, ci.product_fee, ci.admin_fee, ci.company_product_fee, 
		ci.pg_delivery_fee, ci.payment_at, ci.created_at, ci.status,
		cp.cashout_fee_fix_value "company_product_cashout_fee_fix_value",
		cp.cashout_fee_percentage "company_product_cashout_fee_percentage",
		cih.virtual_account, 
		jsonb_agg(
			CASE WHEN cit.id IS NOT NULL THEN 
			json_build_object(
				'partner_name', p."name",
				'ref_invoice_number',cit.ref_invoice_number,
				'amount',cit.amount,
				'note',cit.note,
				'status',cit.status
			)
			END
		) items`).
		Joins(`INNER JOIN company_products cp ON cp.id = ci.company_product_id`).
		Joins(`INNER JOIN payment_providers p_prov ON p_prov.id = ci.payment_provider_id `).
		Joins(`INNER JOIN payment_channels  p_chan ON p_chan.id = ci.payment_channel_id  `).
		Joins(`LEFT JOIN cash_in_transaction_items cit ON cit.cash_in_transaction_id = ci.id AND cit.deleted_at IS NULL`).
		Joins(`INNER JOIN partners p ON p.id = cit.partner_id `).
		Joins(`LEFT JOIN cash_in_transaction_histories cih ON cih.cash_in_transaction_id = ci.id AND cih.deleted_at IS NULL AND cih.payment_status = ?`, constants.PaymentDraft).
		Where("ci.deleted_at IS NULL")
		// Where(`DATE_TRUNC('day',ci.created_at AT TIME ZONE 'Asia/Jakarta') >= ? AND DATE_TRUNC('day',ci.created_at AT TIME ZONE 'Asia/Jakarta') <= ?`, req.StartDatetime.Format(timepkg.TimeDateFormat), req.EndDatetime.Format(timepkg.TimeDateFormat)).
		// Where(`ci.payment_at >= ? AND ci.payment_at < ?`, startDate.Unix(), endDate.Unix())

	switch req.DateFilterType {
	case "created":
		q.Where(`DATE_TRUNC('day',ci.created_at AT TIME ZONE 'Asia/Jakarta') >= ? AND DATE_TRUNC('day',ci.created_at AT TIME ZONE 'Asia/Jakarta') <= ?`, req.StartDatetime.Format(timepkg.TimeDateFormat), req.EndDatetime.Format(timepkg.TimeDateFormat))
	case "settlement":
		q.Where(`ci.payment_at >= ? AND ci.payment_at < ?`, startDate.Unix(), endDate.Unix())
	default:
		q.Where(`DATE_TRUNC('day',ci.created_at AT TIME ZONE 'Asia/Jakarta') >= ? AND DATE_TRUNC('day',ci.created_at AT TIME ZONE 'Asia/Jakarta') <= ?`, req.StartDatetime.Format(timepkg.TimeDateFormat), req.EndDatetime.Format(timepkg.TimeDateFormat))
	}

	if len(req.Statuses) > 0 {
		q.Where(`ci.status IN ?`, req.Statuses)
	}
	if len(req.PartnerID) > 0 {
		q.Where(`p.id IN ?`, req.PartnerID)
	}
	if len(req.PaymentProviderID) > 0 {
		q.Where(`ci.payment_provider_id IN ?`, req.PaymentProviderID)
	}
	if len(req.PaymentChannelID) > 0 {
		q.Where(`ci.payment_channel_id IN ?`, req.PaymentChannelID)
	}
	if req.CompanyID > 0 {
		q.Where(`ci.company_id = ?`, req.CompanyID)
	}

	q.Group("ci.id, cp.id, p_prov.id, p_chan.id, cih.virtual_account").Order("ci.id ASC")
	err = q.Find(&res).Error

	return
}

func (r *defautCashinTransaction) GetLastCashinHistoryByVaAndStatus(ctx context.Context, va string, status string) (resp *domain.CashInTransactionHistories, err error) {
	q := r.db.Where("deleted_at IS NULL AND virtual_account = ?", va)
	if len(status) > 0 {
		q = q.Where("status = ?", "pending")
	}

	err = q.Order("id desc").Take(&resp).Error
	return
}

// UpdateProductFee updates the product_fee for transactions with the given productId
func (r *defautCashinTransaction) UpdateProductFee(ctx context.Context, productId int, feeFix, feePercent float64) (err error) {
	// Use raw SQL to update product_fee as feeFix + (feePercent / 100 * total)
	query := `
		UPDATE cash_in_transactions
		SET company_product_fee = ROUND(? + (? / 100.0 * total)),
		    updated_at = ?
		WHERE deleted_at IS NULL
		AND company_product_id = ?
		AND payment_status IN (?)
	`
	err = r.db.WithContext(ctx).Exec(
		query,
		feeFix,
		feePercent,
		time.Now(),
		productId,
		[]string{constants.CashInStatusPending, constants.CashInStatusPaid},
	).Error
	if err != nil {
		return err
	}

	return nil
}

func (r *defautCashinTransaction) GetTotalCompanyProductFees(ctx context.Context, cashinId []int64) (fees float64, err error) {
	err = r.db.WithContext(ctx).Table("cash_in_transactions").Select("SUM(company_product_fee)").Where("id IN ? AND deleted_at IS NULL", cashinId).Take(&fees).Error
	return
}
