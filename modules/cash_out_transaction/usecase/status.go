package usecase

import (
	"context"
	"encoding/json"
	"fmt"
	"net/http"
	"time"

	"repo.nusatek.id/moaja/backend/libraries/logger"
	errors "repo.nusatek.id/moaja/backend/libraries/service-utils/errors"
	"repo.nusatek.id/nusatek/payment/domain"
	"repo.nusatek.id/nusatek/payment/infrastructure/xendit"
	"repo.nusatek.id/nusatek/payment/infrastructure/xfers"
	"repo.nusatek.id/nusatek/payment/utils/constants"
)

func (s *defaultCashOutUsecase) updateStatusXenditCallback(ctx context.Context, resp xendit.DisbursementCheckStatusResponse, cashOut *domain.CashOutTransactions) error {
	var status string
	var paymentAt int
	switch resp.Status {
	case constants.DisbursementXenditComplete:
		status = constants.CashOutStatusDone
		paymentAt = int(time.Now().Unix())

	default:
		status = constants.CashOutStatusFailed
	}

	xfResp, _ := json.Marshal(resp)
	cashOut.Status = status
	cashOut.PaymentAt = paymentAt
	cashOut.PgReferenceInformation = string(xfResp)
	err := s.wachingUpdateStatusCashOut(ctx, cashOut)
	if err != nil {
		return err
	}

	return nil
}

func (s *defaultCashOutUsecase) updateStatusXfersCallback(ctx context.Context, resp xfers.DisbursementResponse, cashOut *domain.CashOutTransactions) error {
	var status string
	var paymentAt int
	switch resp.Data.Attributes.Status {
	case constants.DisbursementXfersCompleted:
		status = constants.CashInItemStatusDone
		paymentAt = int(time.Now().Unix())

	default:
		status = constants.CashInStatusFailed
	}

	xfResp, _ := json.Marshal(resp)
	cashOut.Status = status
	cashOut.PaymentAt = paymentAt
	cashOut.PgReferenceInformation = string(xfResp)
	err := s.wachingUpdateStatusCashOut(ctx, cashOut)
	if err != nil {
		return err
	}

	return nil
}

func (s *defaultCashOutUsecase) wachingUpdateStatusCashOut(ctx context.Context, req *domain.CashOutTransactions) error {
	tx := s.cashInRepo.BeginTrans()
	defer tx.Rollback()
	_, err := s.cashOutRepository.Update(ctx, req, tx)
	if err != nil {
		err = errors.SetErrorMessage(http.StatusInternalServerError, "[cashout callback] failed to update cashout status")
		logger.Error(ctx, err.Error())
		return err
	}

	reqHistory := domain.CashOutTransactionHistories{
		CashOutTransactionID: req.ID,
		Status:               req.Status,
		Description:          "callback receiver",
	}
	_, err = s.cashOutRepository.CreateHistory(ctx, &reqHistory, tx)
	if err != nil {
		err = errors.SetErrorMessage(http.StatusInternalServerError, "[cashout callback] failed to create history cashout")
		logger.Error(ctx, err.Error())
		return err
	}

	_ = s.setCacheCashOutTransaction(ctx, req)

	var cashInIds = []int{}

	cashoutDoneAt := time.Now()

	partner, err := s.partnerRepo.GetPartnerByID(ctx, fmt.Sprintf("%d", req.PartnerID))
	if err != nil {
		err = errors.SetErrorMessage(http.StatusInternalServerError, err.Error())
		logger.Error(ctx, "[cashout callback] get partner", logger.Err(err))
		return err
	}

	var cashoutCB *domain.CashOutCallback
	var cashInStatusMap = make(map[string]map[string]string)
	if req.Status == constants.CashOutStatusDone {
		detailCashOut, err := s.cashOutRepository.GetCashOutTransactionDetailsByCashOutId(ctx, req.ID)
		if err != nil {
			logger.Error(ctx, "[cashout callback] error get data cashout transcation detail")
			return err
		}

		cashInItemIds := []int{}
		for _, v := range detailCashOut {
			cashInItemIds = append(cashInItemIds, v.CashInTransactionItemID)
		}

		reqItems, err := s.cashInRepo.GetCashinTransactionItemsByIdsWithCashIn(ctx, cashInItemIds)
		if err != nil {
			logger.Error(ctx, "[cashout callback] error get data transaction item")
			return err
		}

		company, err := s.companyService.GetCompanyById(ctx, fmt.Sprintf("%d", partner.CompanyID))
		if err != nil {
			err = errors.SetErrorMessage(http.StatusInternalServerError, err.Error())
			logger.Error(ctx, "[cashout callback] get company", logger.Err(err))
			return err
		}
		var product domain.CompanyProducts
		if req.CompanyProductID != nil {
			productData, err := s.comProductRepo.GetCompanyProductById(ctx, *req.CompanyProductID)
			if err != nil {
				err = errors.SetErrorMessage(http.StatusInternalServerError, err.Error())
				logger.Error(ctx, "[cashout callback] get company product", logger.Err(err))
				return err
			}
			product = *productData
		}

		cashoutCallback := domain.CashOutCallback{
			Operation:   domain.CashoutCallbackUpdated,
			CompanyCode: company.Code,
			ProductName: product.ProductName,
			ProductCode: product.Code,
			PartnerName: partner.Name,
			PartnerCode: partner.Code,
			BatchNumber: req.BatchNumber,
			Status:      req.Status,
		}

		cashInIdMap := make(map[int]float64)
		for _, v := range reqItems {
			cashInIdMap[v.CashInTransactionID] += v.Amount
			cashoutCallback.CashoutItems = append(cashoutCallback.CashoutItems, domain.CashOutCallbackCashoutItems{
				CashIn: domain.CashOutCallbackCashin{
					InvoiceNumber: v.CashInInvoiceNumber,
					//status and payment status will be defined below
				},
				RefInvoiceNumber: v.RefInvoiceNumber,
				ItemName:         v.ItemName,
				Status:           constants.CashInStatusDone,
			})
		}
		cashoutCB = &cashoutCallback

		err = s.cashInRepo.UpdateItemStatusBulk(ctx, tx, cashInItemIds, req.Status)
		if err != nil {
			logger.Error(ctx, "[cashout callback] failed update data transaction items")
			return err
		}

		reqCompanyCashFlows := []domain.CompanyCashFlows{}
		for k, v := range cashInIdMap {
			cashInIds = append(cashInIds, k)
			reqCompanyCashFlows = append(reqCompanyCashFlows, domain.CompanyCashFlows{
				CompanyID:             req.CompanyID,
				CashInTransactionID:   k,
				CashOutTransactionID:  req.ID,
				Debit:                 0,
				Credit:                v,
				Description:           "cash out transaction company flow",
				AdditionalInformation: req.PgReferenceInformation,
			})
		}

		err = s.cashInRepo.CreateCompanyCashFlowBulk(ctx, reqCompanyCashFlows, tx)
		if err != nil {
			err = errors.SetErrorMessage(http.StatusInternalServerError, err.Error())
			logger.Error(ctx, "[cashout callback] create company cashflows bulk", logger.Err(err))
			return err
		}

		// create histories and define cash in status
		// get item count for define status
		cashInWithItemCounts, err := s.cashInRepo.GetAllByIDsWithItemCount(ctx, tx, cashInIds)
		if err != nil {
			err = errors.SetError(http.StatusInternalServerError, err.Error())
			logger.Error(ctx, err.Error())
			return err
		}

		var histories []domain.CashInTransactionHistories
		for _, cashIn := range cashInWithItemCounts {
			//define status
			var cashInstatus = constants.DefineCashInStatusFromItemStatuses(cashIn.Status, cashIn.ItemStatuses.Strings())
			logger.Info(ctx, "debug status watching cashout status", logger.String("cashin status", cashInstatus), logger.String("cashIn.Status", cashIn.Status))
			cashInStatusMap[cashIn.InvoiceNumber] = make(map[string]string)
			cashInStatusMap[cashIn.InvoiceNumber]["paymentStatus"] = cashIn.PaymentStatus
			cashInStatusMap[cashIn.InvoiceNumber]["status"] = cashInstatus
			if cashInstatus == cashIn.Status {
				continue
			}

			err = s.cashInRepo.UpdateStatusCashInTransaction(ctx, tx, cashIn.ID, cashInstatus)
			if err != nil {
				err = errors.SetError(http.StatusInternalServerError, fmt.Sprintf("update cash in status error %v", err))
				logger.Error(ctx, err.Error())
				return err
			}
			// apend histories
			histories = append(histories, domain.CashInTransactionHistories{
				CashInTransactionID:    cashIn.ID,
				Description:            "done cash in",
				PaymentStatus:          "",
				Status:                 cashInstatus,
				VirtualAccount:         "",
				InvoiceNumber:          cashIn.InvoiceNumber,
				ProviderId:             cashIn.PaymentProviderID,
				ChannelId:              cashIn.PaymentChannelID,
				PgReferenceInformation: "{}",
				CreatedAt:              cashoutDoneAt,
				UpdatedAt:              cashoutDoneAt,
			})
		}

		if len(histories) > 0 {
			// create histories bulk
			err = s.cashInRepo.CreateBulkCashinTransactionHistory(ctx, tx, histories)
			if err != nil {
				err = errors.SetError(http.StatusInternalServerError, fmt.Sprintf("create bulk cash in histories %v", err))
				logger.Error(ctx, err.Error())
				return err
			}
		}
	}

	err = tx.Commit().Error
	if err != nil {
		err = errors.SetErrorMessage(http.StatusInternalServerError, err.Error())
		logger.Error(ctx, "error ommit", logger.Err(err))
		return err
	}

	if req.Status == constants.CashOutStatusDone {
		// send email
		if partner.IsSendEmail {
			_ = s.SendCashoutEmail(ctx, req.ID)
		}

		// publish cashout event
		if cashoutCB != nil {
			for i := range cashoutCB.CashoutItems {
				invNumber := cashoutCB.CashoutItems[i].CashIn.InvoiceNumber
				cashoutCB.CashoutItems[i].CashIn.PaymentStatus = cashInStatusMap[invNumber]["paymentStatus"] // replace current status
				cashoutCB.CashoutItems[i].CashIn.Status = cashInStatusMap[invNumber]["status"]               // replace current status
			}
			errPub := s.cashinUc.PublishCashOut(ctx, *cashoutCB)
			if errPub != nil {
				logger.Error(ctx, fmt.Sprintf("error publish cashin item to client %v", errPub))
			}
		}
	}

	return nil
}
