package usecase

import (
	"bytes"
	"context"
	"encoding/json"
	"fmt"
	"log"
	"math"
	"math/rand/v2"
	"net/http"
	"strconv"
	"strings"
	"sync"
	"time"

	"github.com/leekchan/accounting"
	"github.com/xuri/excelize/v2"
	"gorm.io/gorm"
	"repo.nusatek.id/moaja/backend/libraries/logger"
	messagebrokerConfig "repo.nusatek.id/moaja/backend/libraries/message-broker/config"
	messagebrokerMessage "repo.nusatek.id/moaja/backend/libraries/message-broker/message"
	errors "repo.nusatek.id/moaja/backend/libraries/service-utils/errors"

	"repo.nusatek.id/nusatek/payment/domain"
	emailclient "repo.nusatek.id/nusatek/payment/infrastructure/email_client"
	infrastructure "repo.nusatek.id/nusatek/payment/infrastructure/messagebroker"
	"repo.nusatek.id/nusatek/payment/infrastructure/telegram"
	"repo.nusatek.id/nusatek/payment/infrastructure/xendit"
	"repo.nusatek.id/nusatek/payment/infrastructure/xfers"
	"repo.nusatek.id/nusatek/payment/infrastructure/xfers/disbursement_request"
	"repo.nusatek.id/nusatek/payment/logutil"
	"repo.nusatek.id/nusatek/payment/modules/cash_out_transaction/entity"
	"repo.nusatek.id/nusatek/payment/utils"
	"repo.nusatek.id/nusatek/payment/utils/constants"
	"repo.nusatek.id/nusatek/payment/utils/model"
	"repo.nusatek.id/nusatek/payment/utils/str"
	"repo.nusatek.id/nusatek/payment/utils/timepkg"
)

func (cu *defaultCashOutUsecase) GetByID(ctx context.Context, ID int) (resp domain.CashOutTransactions, err error) {
	resp, err = cu.cashOutRepository.GetByID(ctx, ID)
	if err != nil {
		err = errors.SetErrorMessage(http.StatusNotFound, "cash out transaction not found")
		logger.Error(ctx, err.Error())
		return
	}
	return
}

func (cu *defaultCashOutUsecase) GetDetailByID(ctx context.Context, ID int) (resp domain.CashOutTransactionDetail, err error) {
	resp, err = cu.cashOutRepository.GetDetailByID(ctx, ID)
	if err != nil {
		logger.Error(ctx, err.Error())
		if err == gorm.ErrRecordNotFound {
			err = errors.SetErrorMessage(http.StatusNotFound, "cash out transaction not found")
			return
		}

		err = errors.SetErrorMessage(http.StatusInternalServerError, fmt.Sprintf("error get cash out transaction detail %v", err))
		return
	}

	cashInInfo, err := cu.cashOutRepository.GetCashInInfo(ctx, []int64{int64(ID)})
	if err != nil {
		logger.Error(ctx, err.Error())
		err = errors.SetErrorMessage(http.StatusInternalServerError, fmt.Sprintf("error error get transaction fee %v", err))
		return
	}
	for _, v := range cashInInfo {
		resp.CashInTotalAmount.TotalDiscount += v.Discount
		resp.CashInTotalAmount.TotalVoucher += v.Voucher
		resp.CashInTotalAmount.TransactionFee += v.ProductFee
	}

	return
}

func (cu *defaultCashOutUsecase) List(ctx context.Context, param map[string]interface{}, orderParam map[string]interface{}) (res []domain.CashOutTransactionDetail, totalItem int64, err error) {
	res, totalItem, err = cu.cashOutRepository.ListCashOut(ctx, param, orderParam)
	if err != nil {
		err = errors.SetError(http.StatusInternalServerError, err.Error())
		logger.Error(ctx, err.Error())
		return
	}
	var cashoutIds []int64
	for _, v := range res {
		cashoutIds = append(cashoutIds, v.ID)
	}

	cashinData, err := cu.cashOutRepository.GetCashInInfo(ctx, cashoutIds)
	if err != nil {
		err = errors.SetError(http.StatusInternalServerError, err.Error())
		logger.Error(ctx, err.Error())
	}

	cashinMap := make(map[int64][]domain.CashOutCashInInfo)
	for _, v := range cashinData {
		cashinMap[v.CashOutID] = append(cashinMap[v.CashOutID], v)
	}
	// fmt.Printf("cashinMap: %+v\n", cashinMap)
	for i, v := range res {
		for _, v := range cashinMap[v.ID] {
			res[i].CashInTotalAmount.TotalDiscount += v.Discount
			res[i].CashInTotalAmount.TotalVoucher += v.Voucher
			res[i].CashInTotalAmount.TransactionFee += v.ProductFee
		}
	}

	return
}

func (s *defaultCashOutUsecase) CheckStatusCashOutAPI(ctx context.Context, cashOutId, auth string) (resp interface{}, err error) {
	company, err := s.companyService.ValidatorCompanySecret(ctx, auth)
	if err != nil {
		return
	}

	param := map[string]interface{}{
		"key":   "cashOut_id",
		"value": cashOutId,
	}

	orderParam := map[string]interface{}{
		"start": 1,
		"limit": 1,
	}

	cashOutArr, _, err := s.cashOutRepository.ListCashOut(ctx, param, orderParam)
	if err != nil {
		err = errors.SetError(http.StatusInternalServerError, err.Error())
		logger.Error(ctx, err.Error())
		return
	}

	if len(cashOutArr) == 0 {
		err = errors.SetErrorMessage(http.StatusNotFound, "cashout transaction not found")
		logger.Error(ctx, err.Error())
		return
	}

	csByte, _ := json.Marshal(cashOutArr)
	var cashOutArr2 []map[string]interface{}
	_ = json.Unmarshal(csByte, &cashOutArr2)

	if fmt.Sprintf("%v", cashOutArr2[0]["company_id"]) != strconv.Itoa(company.ID) {
		err = errors.SetErrorMessage(http.StatusNotFound, "cashout transaction not found in company")
		logger.Error(ctx, err.Error())
		return
	}

	resp = cashOutArr2[0]
	return
}

func (cu *defaultCashOutUsecase) ListAPI(ctx context.Context, param map[string]interface{}, orderParam map[string]interface{}, auth string) (res []domain.CashOutTransactionDetail, totalItem int64, err error) {
	company, err := cu.companyService.ValidatorCompanySecret(ctx, auth)
	if err != nil {
		return
	}

	param["key"] = "company_id"
	param["value"] = company.ID
	res, totalItem, err = cu.cashOutRepository.ListCashOut(ctx, param, orderParam)
	if err != nil {
		err = errors.SetError(http.StatusInternalServerError, err.Error())
		logger.Error(ctx, err.Error())
		return
	}

	return
}

func (cu *defaultCashOutUsecase) RequestDisbursement(ctx context.Context, req entity.UpdateRequest, cashOutId, userId int) (err error) {
	cashOut, err := cu.cashOutRepository.GetByID(ctx, cashOutId)
	if err != nil {
		err = errors.SetErrorMessage(http.StatusNotFound, err.Error())
		logger.Error(ctx, err.Error())
		return
	}

	// constants.CashOutStatusFailed need to include when cashout status is failed, and we need to request again
	if !str.Contains([]string{constants.CashOutStatusReady, constants.CashOutStatusFailed, constants.CashOutStatusRejected}, cashOut.Status) {
		err = errors.SetErrorMessage(http.StatusBadRequest, "invalid status")
		logger.Error(ctx, err.Error())
		return
	}

	if cashOut.Total < 15000 {
		err = errors.SetErrorMessage(http.StatusBadRequest, "minimum cash out amount Rp.15000, please delete item transaction and let it join other item transaction")
		logger.Error(ctx, err.Error())
		return
	}
	cashOut.PaymentProviderID = req.ProviderId

	if req.Scheduler {
		timeStamp, errRes := time.Parse("2006/01/02 15:04:05", req.SchedulerAt)
		loc, err := time.LoadLocation(utils.TimeJakartaLoc)
		if err != nil {
			err = errors.SetErrorMessage(http.StatusInternalServerError, "wrong location time")
			logger.Error(ctx, err.Error())
			return err
		}
		timeStamp = time.Date(timeStamp.Year(), timeStamp.Month(), timeStamp.Day(), timeStamp.Hour(), timeStamp.Minute(), timeStamp.Second(), timeStamp.Nanosecond(), loc)
		if errRes != nil {
			err = errors.SetErrorMessage(http.StatusBadRequest, "wrong format timestamp")
			logger.Error(ctx, err.Error())
			return err
		}
		cashOut.DisbursementScheduleAt = timeStamp.Unix()
	}

	_, err = cu.providerService.GetPaymentProviderById(ctx, strconv.Itoa(cashOut.PaymentProviderID))
	if err != nil {
		err = errors.SetErrorMessage(http.StatusNotFound, "provider id not found")
		logger.Error(ctx, err.Error())
		return
	}

	// Calculate partner admin fee
	partnerRes, err := cu.partnerRepo.GetPartnerByID(ctx, strconv.Itoa(cashOut.PartnerID))
	if err != nil {
		err = errors.SetErrorMessage(http.StatusNotFound, "partner not found")
		logger.Error(ctx, err.Error())
		return
	}

	cashOut.FeePaymentStatus = true
	if partnerRes.FeeBehind {
		cashOut.FeePaymentStatus = false
	}

	channelProviderMapping, err := cu.companyMappingRepo.GetCompanyProviderMapping(ctx, cashOut.CompanyID, cashOut.PaymentProviderID, cashOut.PaymentChannelID, constants.CashOutCapability)
	if err != nil || channelProviderMapping.ID == 0 {
		err = errors.SetErrorMessage(http.StatusNotFound, "payment provider does not support cashout with this channel")
		logger.Error(ctx, err.Error())
		return
	}
	cashOut.PlatformFee = channelProviderMapping.FeeFixValue
	if channelProviderMapping.FeePercentage > 0 {
		if cashOut.FeePaymentStatus {
			cashOut.PlatformFee += (cashOut.Total - cashOut.AdminFee) * channelProviderMapping.FeePercentage / 100
		} else {
			cashOut.PlatformFee += cashOut.Total * channelProviderMapping.FeePercentage / 100
		}
	}
	cashOut.PlatformFee = math.Round(cashOut.PlatformFee)

	tx := cu.cashInRepo.BeginTrans()
	defer tx.Rollback()

	nextStatus := constants.CashOutStatusRequested
	histDesc := "request cashout transaction"

	if cashOut.Status == constants.CashOutStatusRejected { // if current status is rejected, then patch to ready
		nextStatus = constants.CashOutStatusReady
		histDesc = "request cashout revision"
	}

	loc := utils.GetLocalTime()
	cashOut.Status = nextStatus
	cashOut.RequestedBy = &userId
	cashOut.RequestedAt = &loc
	_, err = cu.cashOutRepository.Update(ctx, &cashOut, tx)
	if err != nil {
		logger.Error(ctx, err.Error())
		return
	}

	ch := new(domain.CashOutTransactionHistories)
	ch.Status = nextStatus
	ch.Description = histDesc
	ch.CashOutTransactionID = cashOut.ID
	_, err = cu.cashOutRepository.CreateHistory(ctx, ch, tx)
	if err != nil {
		logger.Error(ctx, err.Error())
		return
	}

	tx.Commit()

	return
}

func (cu *defaultCashOutUsecase) DisbursementProcess(ctx context.Context) {
	list, _ := cu.cashOutRepository.RequestDisbursement(ctx)
	for _, v := range list {
		go func() {
			err := cu.DisbursementProcessSingle(context.Background(), v)
			if err != nil {
				logger.Error(ctx, err.Error())
			}
		}()
	}
}

func (cu *defaultCashOutUsecase) DisbursementProcessSingle(ctx context.Context, v domain.CashOutTransactions) (err error) {
	providerRes, err := cu.providerService.GetPaymentProviderById(ctx, strconv.Itoa(v.PaymentProviderID))
	if err != nil {
		err = errors.SetErrorMessage(http.StatusNotFound, "provider id not found")
		logger.Error(ctx, err.Error())
		return err
	}

	providerMapping, err := cu.companyMappingService.GetCompanyProviderMappingByCompanyIdAndProviderId(ctx, strconv.Itoa(v.CompanyID), strconv.Itoa(v.PaymentProviderID))
	if err != nil {
		logger.Error(ctx, "payment provider not found in company", logger.Err(err))
		return err
	}

	mappingChannelResp, err := cu.channelMappingRepo.GetChannelMappingByProviderChannelIdAndCapability(ctx, strconv.Itoa(v.PaymentProviderID), strconv.Itoa(v.PaymentChannelID), strconv.Itoa(constants.CashOutCapability))
	if err != nil {
		logger.Error(ctx, "channel mapping not found", logger.Err(err))
		return err
	}

	// Calculate total cashout amount
	amount := v.Total
	if v.FeePaymentStatus {
		amount -= v.AdminFee
	}
	// amount += v.PlatformFee // platform fee only used as information (hanya digunakan sebagai catatan pengeluaran)

	var statusCodeResp int
	switch true {
	case strings.Contains(strings.ToLower(providerRes.Name), constants.ProviderXfers):
		cashOutReq := disbursement_request.AttributeValue{
			Amount:      amount,
			ReferenceID: v.BatchNumber,
			Description: "",
			DisbursementMethod: disbursement_request.DisbursementMethod{
				Type:                  "bank_transfer",
				BankShortCode:         mappingChannelResp.ProviderChannelCode,
				BankAccountNo:         v.PartnerAccountNumber,
				BankAccountHolderName: v.PartnerAccountName,
			},
		}

		credential, err := xfers.GetXfersCredential(providerMapping.ProviderSecrets)
		if err != nil {
			logger.Error(ctx, err.Error())
			return err
		}

		resp, statusCode, err := cu.xfersRest.Disbursements(ctx, cashOutReq, credential)
		if err != nil {
			logger.Error(ctx, err.Error())
			return err
		}

		pg, _ := json.Marshal(resp)
		v.PgReferenceInformation = string(pg)
		statusCodeResp = statusCode

	case strings.Contains(strings.ToLower(providerRes.Name), constants.ProviderXendit):
		credential, err := xendit.GetXenditCredential(providerMapping.ProviderSecrets)
		if err != nil {
			logger.Error(ctx, err.Error())
			return err
		}

		// check disbursement by external id/batch number
		if err := cu.isXenditDisbursmentExists(ctx, v, credential, amount); err != nil {
			logger.Error(ctx, err.Error())
			return err
		}

		xenditReq := xendit.DisbursementRequest{
			AccountHolderName: v.PartnerAccountName,
			AccountNumber:     v.PartnerAccountNumber,
			Amount:            amount,
			BankCode:          mappingChannelResp.ProviderChannelCode,
			Description:       "disbursement partner",
			ExternalID:        v.BatchNumber,
		}
		resp, statusCode, err := cu.xenditRest.CreateDisbursement(ctx, xenditReq, credential)
		if err != nil {
			logger.Error(ctx, err.Error())
			return fmt.Errorf("Disbursement gagal. Silakan hubungi Administrator.")
		}

		pg, _ := json.Marshal(resp)
		v.PgReferenceInformation = string(pg)
		statusCodeResp = statusCode

	default:
		err = errors.SetErrorMessage(http.StatusNotFound, "payment provider is not yet available")
		logger.Error(ctx, err.Error())
		return err
	}

	if statusCodeResp == http.StatusCreated || statusCodeResp == http.StatusOK {
		v.Status = constants.CashOutStatusApproved
		history := &domain.CashOutTransactionHistories{
			Description: "disburse approved and already create disbursement to payment gateway",
			Status:      constants.CashOutStatusApproved,
		}
		locTime := utils.GetLocalTime()
		if v.FeePaymentStatus {
			v.FeePaymentAt = &locTime
		}
		v.CashOutTransactionHistories = history
		v.ApprovedAt = &locTime
		tx := cu.cashInRepo.BeginTrans()
		defer tx.Rollback()

		_, err = cu.cashOutRepository.Update(ctx, &v, tx)
		if err != nil {
			logger.Error(ctx, err.Error())
			return err
		}

		tx.Commit()

	} else {
		cashout, _ := json.Marshal(v)
		logger.Error(ctx, string(cashout))
		err = errors.SetErrorMessage(http.StatusNotFound, "disbursement fail")
		return err
	}

	return nil
}

func (cu *defaultCashOutUsecase) isXenditDisbursmentExists(ctx context.Context, v domain.CashOutTransactions, credential xendit.Credential, amount float64) error {
	resps, err := cu.xenditRest.CheckByExternalId(ctx, v.BatchNumber, credential)
	if err != nil {
		return fmt.Errorf("Disbursement gagal. Silakan hubungi Administrator.")
	}

	if len(resps) > 0 || resps != nil {
		for _, resp := range resps {
			if resp.Amount == int64(amount) && resp.Status == "COMPLETED" {
				history := &domain.CashOutTransactionHistories{
					Description: "Transaksi Gagal.\nTransaksi dengan nomor Virtual Account dan jumlah yang sama telah diproses sebelumnya.",
					Status:      constants.CashOutStatusFailed,
				}

				locTime := utils.GetLocalTime()

				v.CashOutTransactionHistories = history
				v.Status = constants.CashOutStatusFailed
				v.UpdatedAt = locTime

				tx := cu.cashInRepo.BeginTrans()
				defer tx.Rollback()

				_, err = cu.cashOutRepository.Update(ctx, &v, tx)
				if err != nil {
					return err
				}

				tx.Commit()

				return fmt.Errorf("disbursement already exist")
			}
		}
	}

	return nil
}

func (s *defaultCashOutUsecase) SendCashoutEmail(ctx context.Context, coId int) error {
	cashout, err := s.getCashoutData(ctx, coId)
	if err != nil {
		return err
	}

	partner, err := s.partnerRepo.GetPartnerByID(ctx, fmt.Sprintf("%d", cashout.PartnerID))
	if err != nil {
		err = errors.SetErrorMessage(http.StatusInternalServerError, err.Error())
		logger.Error(ctx, "[cashout callback] get partner", logger.Err(err))
		return err
	}

	// send email
	if !partner.IsSendEmail {
		err = errors.SetErrorMessage(http.StatusBadRequest, "Disbursement email is currently inactive in this partner")
		return err
	}

	excelFile, err := s.generateExcelFile(ctx, cashout)
	if err != nil {
		return err
	}

	buff, err := excelFile.WriteToBuffer()
	if err != nil {
		return errors.SetErrorMessage(http.StatusBadRequest, fmt.Sprintf("error save write to buffer excel %v", err))
	}

	emailData, err := s.prepareEmailData(ctx, cashout)
	if err != nil {
		return err
	}

	emailClientMsg, err := s.generateTemplateEmail(ctx, emailData, buff)
	if err != nil {
		return err
	}

	return s.sendEmail(ctx, emailClientMsg, emailData)
}

func (s *defaultCashOutUsecase) getCashoutData(ctx context.Context, coId int) (*domain.CashOutTransactions, error) {
	cashout, err := s.cashOutRepository.GetByID(ctx, coId)
	if err != nil {
		return nil, errors.SetErrorMessage(http.StatusInternalServerError, "get cashout data error")
	}
	return &cashout, nil
}

func (s *defaultCashOutUsecase) generateExcelFile(ctx context.Context, cashout *domain.CashOutTransactions) (*excelize.File, error) {
	data, err := s.cashOutRepository.Export(ctx, &domain.CashoutExportReq{
		CashoutID: int(cashout.ID),
	})
	if err != nil {
		return nil, errors.SetErrorMessage(http.StatusInternalServerError, "get data export error")
	}

	excelFile, err := s.exportExcel(ctx, domain.CashoutExportReq{CashoutInfo: cashout, ShowFee: cashout.FeePaymentStatus}, data)
	if err != nil {
		return nil, errors.SetErrorMessage(http.StatusInternalServerError, fmt.Sprintf("error save excel %v", err))
	}

	return excelFile, nil
}

func (s *defaultCashOutUsecase) prepareEmailData(ctx context.Context, cashout *domain.CashOutTransactions) (*emailData, error) {
	partner, err := s.partnerRepo.GetPartnerByID(ctx, fmt.Sprintf("%d", cashout.PartnerID))
	if err != nil {
		return nil, errors.SetErrorMessage(http.StatusInternalServerError, fmt.Sprintf("error get partner %v", err))
	}

	company, err := s.companyService.GetCompanyById(ctx, fmt.Sprintf("%d", cashout.CompanyID))
	if err != nil {
		return nil, errors.SetErrorMessage(http.StatusInternalServerError, fmt.Sprintf("error get company %v", err))
	}

	productName := ""
	if cashout.CompanyProductID != nil {
		product, err := s.comProductRepo.GetCompanyProductById(ctx, *cashout.CompanyProductID)
		if err != nil {
			return nil, errors.SetErrorMessage(http.StatusInternalServerError, fmt.Sprintf("error get product %v", err))
		}
		productName = product.ProductName
	}

	paymentAt := domain.UnixTime(cashout.PaymentAt)

	return &emailData{
		cashOutID:                int64(cashout.ID),
		companyID:                int64(cashout.CompanyID),
		partnerID:                int64(cashout.PartnerID),
		batchNumber:              cashout.BatchNumber,
		partnerName:              cashout.PartnerName,
		partnerPaymentChannel:    cashout.PartnerBankName,
		partnerBankAccountNumber: cashout.PartnerAccountNumber,
		productName:              productName,
		companyName:              company.Name,
		companyEmail:             company.Email,
		transferredDate:          paymentAt.Time(timepkg.TimeJakartaLoc).Format(timepkg.TimeDateExportFormat),
		totalDisbursement:        fmt.Sprintf("%.2f", cashout.Total),
		recipientEmail:           partner.Email,
		recipientEmails:          partner.Emails,
	}, nil
}

type emailData struct {
	cashOutID                int64
	companyID                int64
	partnerID                int64
	batchNumber              string
	partnerName              string
	partnerPaymentChannel    string
	partnerBankAccountNumber string
	productName              string
	companyName              string
	companyEmail             string
	transferredDate          string
	totalDisbursement        string
	recipientEmail           string
	recipientEmails          []string
}

func (s *defaultCashOutUsecase) generateTemplateEmail(ctx context.Context, data *emailData, buff *bytes.Buffer) ([]emailclient.SendMsgReq, error) {
	template, err := s.emailTemplate.GetTemplateByCompanyAndPartner(ctx, data.companyID, data.partnerID)
	if err != nil {
		return nil, errors.SetErrorMessage(http.StatusInternalServerError, "get template error")
	}

	var emailClientMsg []emailclient.SendMsgReq

	for _, email := range data.recipientEmails {
		emailMsg := template.Content

		emailMsg = strings.ReplaceAll(emailMsg, "{{partner_name}}", data.partnerName)
		emailMsg = strings.ReplaceAll(emailMsg, "{{partner_payment_channel}}", data.partnerPaymentChannel)
		emailMsg = strings.ReplaceAll(emailMsg, "{{partner_bank_account_number}}", data.partnerBankAccountNumber)
		emailMsg = strings.ReplaceAll(emailMsg, "{{product_name}}", data.productName)
		emailMsg = strings.ReplaceAll(emailMsg, "{{company_name}}", data.companyName)
		emailMsg = strings.ReplaceAll(emailMsg, "{{transferred_date}}", data.transferredDate)
		emailMsg = strings.ReplaceAll(emailMsg, "{{total_disbursement}}", data.totalDisbursement)
		emailMsg = strings.ReplaceAll(emailMsg, "{{recipient_email}}", email)

		emailClientMsg = append(emailClientMsg, emailclient.SendMsgReq{
			Sender:    fmt.Sprintf("%s <%s>", data.companyName, data.companyEmail),
			Recipient: email,
			Subject:   template.Subject,
			Body:      emailMsg,
			Attachs: []emailclient.SendMsgReqAttachment{
				{
					Filename: fmt.Sprintf("Cash Out Report - %s.xlsx", time.Now().In(timepkg.TimeJakartaLoc).Format("********")),
					Buffer:   buff.Bytes(),
				},
			},
			CCs: template.EmailCcs,
			Data: entity.EmailParametersPayload{
				CompanyID:   data.companyID,
				PartnerID:   data.partnerID,
				BatchNumber: data.batchNumber,
			},
		})
	}

	return emailClientMsg, nil
}

func (s *defaultCashOutUsecase) sendEmail(ctx context.Context, emailClientMsgs []emailclient.SendMsgReq, emailData *emailData) error {
	// Channel to collect errors from goroutines
	errChan := make(chan error, len(emailClientMsgs))
	var wg sync.WaitGroup

	// Loop through each email message and send it concurrently
	for _, emailClientMsg := range emailClientMsgs {
		wg.Add(1)

		// Launch a goroutine for sending each email
		go func(emailClientMsg emailclient.SendMsgReq) {
			defer wg.Done()

			time.Sleep(time.Duration(time.Duration(rand.IntN(2)) * time.Second))

			if err := s.emailClient.SendMsg(ctx, emailClientMsg); err != nil {
				if errTelegram := telegram.SendTelegram(telegram.Message{
					CompanyName:       emailData.companyName,
					BatchNumber:       emailData.batchNumber,
					PartnerName:       emailData.partnerName,
					TotalDisbursement: fmt.Sprintf("%.2s", emailData.totalDisbursement),
					FailedEmail:       emailClientMsg.Recipient,
					ErrorMessage:      err.Error(),
				}); errTelegram != nil {
					logger.Error(ctx, fmt.Sprintf("error sending telegram: %v", errTelegram))
					errChan <- errTelegram
				}

				errChan <- errors.SetErrorMessage(http.StatusInternalServerError, fmt.Sprintf("error sending email: %v", err))
				return
			}

			now := time.Now()

			emailBody, err := json.Marshal(emailClientMsg)
			if err != nil {
				errChan <- errors.SetErrorMessage(http.StatusInternalServerError, fmt.Sprintf("error marshaling email body: %v", err))
				return
			}

			// Save the email log in the repository
			emailLog := entity.EmailLog{
				CashoutID:   emailData.cashOutID,
				CompanyID:   emailData.companyID,
				PartnerID:   emailData.partnerID,
				BatchNumber: emailData.batchNumber,
				Email:       emailClientMsg.Recipient,
				MessageBody: emailBody,
				SendAt:      now,
				CreatedAt:   now,
				UpdatedAt:   now,
			}

			if err := s.cashOutRepository.SaveEmailLog(ctx, emailLog); err != nil {
				logger.Error(ctx, fmt.Sprintf("error saving email log: %v", err))
			}
		}(emailClientMsg)
	}

	wg.Wait()
	close(errChan)

	var errMessages []string
	for err := range errChan {
		if err != nil {
			errMessages = append(errMessages, err.Error())
		}
	}

	if len(errMessages) > 0 {
		return fmt.Errorf("sendEmail encountered errors: %v", errMessages)
	}

	return nil
}

func (c *defaultCashOutUsecase) GenerateQRGoogleAuthorization(ctx context.Context, userId int) (resp model.Qr, err error) {
	secret, userResp, err := c.getSecretGoogleAuthorization(ctx, userId)
	if err != nil {
		return
	}

	googleQR := utils.SetupGoogleAutorization(secret).GenerateGoogleAuthQR(userResp.Email, userResp.Name)
	resp.QR = googleQR
	return
}

func (c *defaultCashOutUsecase) UpdateCashOutTransactionStatus(ctx context.Context, req entity.OTP, userId int, statusUpdate string) (resp *domain.CashOutTransactions, err error) {
	_, err = c.validateOTPCode(ctx, req.OTPCode, userId)
	if err != nil {
		logger.Error(ctx, err.Error())
		return
	}
	if len(req.CashOutTransactionID) == 0 {
		err = errors.SetErrorMessage(http.StatusBadRequest, "you have not selected a transaction")
		logger.Error(ctx, err.Error())
		return
	}
	tx := c.cashInRepo.BeginTrans()
	defer tx.Rollback()

	for i := 0; i < len(req.CashOutTransactionID); i++ {
		cashOut, errRes := c.cashOutRepository.GetByID(ctx, req.CashOutTransactionID[i])
		if errRes != nil {
			err = errors.SetErrorMessage(http.StatusNotFound, "cashout not found")
			logger.Error(ctx, err.Error())
			return
		}
		if cashOut.Status != constants.CashOutStatusRequested {
			err = errors.SetErrorMessage(http.StatusBadRequest, "status has been changed before")
			logger.Error(ctx, err.Error())
			return
		}

		loc := utils.GetLocalTime()
		cashOut.Status = statusUpdate
		cashOut.ApprovedBy = &userId
		cashOut.ApprovedAt = &loc
		updateCashout, errRes := c.cashOutRepository.Update(ctx, &cashOut, tx)
		if errRes != nil {
			err = errors.SetErrorMessage(http.StatusInternalServerError, errRes.Error())
			logger.Error(ctx, err.Error())
			return
		}

		var description = "approval transaction"
		if statusUpdate == constants.CashOutStatusRejected {
			description = req.Reason
		}

		reqHistory := domain.CashOutTransactionHistories{
			CashOutTransactionID: cashOut.ID,
			Status:               statusUpdate,
			Description:          description,
		}
		_, err = c.cashOutRepository.CreateHistory(ctx, &reqHistory, tx)
		if err != nil {
			logger.Error(ctx, err.Error())
			return
		}

		_ = c.deleteCacheCashOutTransaction(ctx, updateCashout.BatchNumber)
		_ = c.setCacheCashOutTransaction(ctx, updateCashout)
	}

	// Update cash in item status into pending & cashout status into false
	if statusUpdate == constants.CashOutStatusFailed {
		err = c.cashOutRepository.RollbackCashInItemByCashout(ctx, tx, req.CashOutTransactionID)
		if err != nil {
			logger.Error(ctx, err.Error())
			return
		}
	}

	tx.Commit()
	return
}

// LockDisbursement attempts to acquire a lock in Redis for a specific transaction ID
func (c *defaultCashOutUsecase) LockDisbursement(ctx context.Context, transactionID int, expiration time.Duration) (bool, error) {
	lockKey := fmt.Sprintf("disburse_lock:%d", transactionID)
	success, err := c.cache.SetNX(ctx, lockKey, "locked", expiration).Result()
	if err != nil {
		return false, fmt.Errorf("error acquiring lock for ID %d: %w", transactionID, err)
	}
	return success, nil
}

// UnlockDisbursement releases the lock in Redis for a specific transaction ID
func (c *defaultCashOutUsecase) UnlockDisbursement(ctx context.Context, transactionID int) error {
	lockKey := fmt.Sprintf("disburse_lock:%d", transactionID)
	_, err := c.cache.Del(ctx, lockKey).Result()
	if err != nil {
		log.Printf("failed to unlock key %s: %v", lockKey, err)
	}
	return err
}

func (c *defaultCashOutUsecase) BulkApproveCashOutTransactionStatus(ctx context.Context, req entity.OTP, userId int) (err error) {
	_, err = c.validateOTPCode(ctx, req.OTPCode, userId)
	if err != nil {
		logger.Error(ctx, "validateOTPCode", logger.Err(err))
		return
	}

	if len(req.CashOutTransactionID) == 0 {
		err = errors.SetErrorMessage(http.StatusBadRequest, "you must select minimum one transaction")
		logger.Error(ctx, err.Error())
		return
	}

	cashOuts, err := c.cashOutRepository.GetRequestedCashOutByIds(ctx, req.CashOutTransactionID)
	if err != nil {
		logger.Error(ctx, "error get cashout", logger.Err(err))
		return
	}

	if len(cashOuts) == 0 {
		err = errors.SetErrorMessage(http.StatusNotFound, "The request can't be processed because a submission with the same Cash Out Number has already been made.")
		logger.Error(ctx, err.Error())
		return
	}

	// add limit concurrent so not too many request to provider
	const maxConcurrent = 5
	var wg sync.WaitGroup
	expiresLock := time.Second * 30

	errChan := make(chan error, len(cashOuts))
	sem := make(chan struct{}, maxConcurrent)

	for _, cashOut := range cashOuts {
		wg.Add(1)
		sem <- struct{}{}
		go func(wg *sync.WaitGroup, cashOut domain.CashOutTransactions) {
			defer wg.Done()
			<-sem

			// Try to acquire lock for this transaction ID
			locked, lockErr := c.LockDisbursement(ctx, cashOut.ID, expiresLock)
			if lockErr != nil || !locked {
				errChan <- fmt.Errorf("transaction with batch number %s is already being processed.", cashOut.BatchNumber)
				logger.Error(ctx, "lock acquisition failed", logger.String("batch", cashOut.BatchNumber), logger.Err(lockErr))
				return
			}

			defer func() {
				if err := c.UnlockDisbursement(ctx, cashOut.ID); err != nil {
					logger.Error(ctx, "unlock approval disbursement failed")
				}
			}()

			cashOut.ApprovedBy = &userId
			err := c.DisbursementProcessSingle(ctx, cashOut)
			if err != nil {
				errChan <- err
				logger.Error(ctx, "error disbursement process", logger.String("batch", cashOut.BatchNumber), logger.Err(err))
			}

		}(&wg, cashOut)
	}
	wg.Wait()
	close(errChan)

	var errMsg string
	for err := range errChan {
		errMsg += fmt.Sprintf("%v\n", err)
	}

	if len(errMsg) > 0 {
		err = errors.SetErrorMessage(http.StatusInternalServerError, errMsg)
		logger.Error(ctx, err.Error())
		return
	}

	return
}

func (s *defaultCashOutUsecase) UpdateCashOutTransactionStatusFeePaymentStatus(ctx context.Context, req entity.FeePaymentStatusRequest, userId int) (err error) {
	_, err = s.validateOTPCode(ctx, req.OTPCode, userId)
	if err != nil {
		return
	}
	if len(req.CashOutTranscationID) == 0 {
		err = errors.SetErrorMessage(http.StatusBadRequest, "you have not selected a transaction")
		logger.Error(ctx, err.Error())
		return
	}

	tx := s.cashInRepo.BeginTrans()
	defer tx.Rollback()

	var totalFee float64
	for i := 0; i < len(req.CashOutTranscationID); i++ {
		cashOut, errRes := s.cashOutRepository.GetByID(ctx, req.CashOutTranscationID[i])
		if errRes != nil {
			err = errors.SetErrorMessage(http.StatusNotFound, "cashout not found")
			logger.Error(ctx, err.Error())
			return
		}
		if cashOut.Status != constants.CashOutStatusReconciled {
			err = errors.SetErrorMessage(http.StatusBadRequest, "payment cash out status has not been reconciled")
			logger.Error(ctx, err.Error())
			return
		}
		if cashOut.FeePaymentStatus {
			err = errors.SetErrorMessage(http.StatusBadRequest, "payment fee status has been changed before")
			logger.Error(ctx, err.Error())
			return
		}
		if req.PartnerId != cashOut.PartnerID {
			err = errors.SetErrorMessage(http.StatusBadRequest, "partners are not the same")
			logger.Error(ctx, err.Error())
			return
		}
		loc := utils.GetLocalTime()
		cashOut.FeePaymentStatus = true
		cashOut.FeePaymentAt = &loc
		updateCashout, errRes := s.cashOutRepository.Update(ctx, &cashOut, tx)
		if errRes != nil {
			err = errors.SetErrorMessage(http.StatusInternalServerError, errRes.Error())
			logger.Error(ctx, err.Error())
			return
		}
		totalFee = totalFee + cashOut.AdminFee
		_ = s.setCacheCashOutTransaction(ctx, updateCashout)
	}

	if req.TotalFee < totalFee {
		err = errors.SetErrorMessage(http.StatusBadRequest, "invalid total fee")
		logger.Error(ctx, err.Error())
		return
	}

	tx.Commit()
	return
}

func (s *defaultCashOutUsecase) CallbackCashOutTransactionProces(ctx context.Context, batchNumber, paymentId, providerName string, body []byte) {
	go func() {
		_ = s.trxReqLogService.UpdateResourceCtx(ctx, batchNumber, logutil.ResourceTypeCashOut)
	}()

	var xfersCredential xfers.Credential
	var xenditCredential xendit.Credential
	var cashOut *domain.CashOutTransactions

	switch providerName {
	case constants.ProviderXfers:
		credential, cash, err := s.getCredentialCashOutXfersCallback(ctx, batchNumber)
		if err != nil {
			logger.Error(ctx, "[cashout] error get credential xfers")
			return
		}
		xfersCredential = credential
		cashOut = cash

	case constants.ProviderXendit:
		credential, cash, err := s.getCredentialCashOutXenditCallback(ctx, batchNumber)
		if err != nil {
			logger.Error(ctx, "[cashout] error get credential xendit")
			return
		}
		xenditCredential = credential
		cashOut = cash
	}

	switch providerName {
	case constants.ProviderXfers:
		respXfers, errRes := s.xfersRest.RetrieveDisbursement(ctx, paymentId, xfersCredential) // TO DO: Implement Backoff Retry
		if errRes == nil {
			err := s.updateStatusXfersCallback(ctx, respXfers, cashOut)
			if err != nil {
				logger.Error(ctx, "[cash out callback] resend callback to queue")
				return
			}
			return
		}

	case constants.ProviderXendit:
		respXendit, errRes := s.xenditRest.CheckStatusDisbursement(ctx, paymentId, xenditCredential)
		if errRes == nil {
			err := s.updateStatusXenditCallback(ctx, respXendit, cashOut)
			if err != nil {
				logger.Error(ctx, "[cash out callback] resend callback to queue")
				return
			}
			return
		}
	}

}

func (c *defaultCashOutUsecase) PushCallbackProviderCashOutTranscation(ctx context.Context, signature string, req interface{}, providerName string) (err error) {
	var keyPublis string
	reqByte, _ := json.Marshal(req)

	switch providerName {
	case constants.ProviderXfers:
		logger.Info(ctx, "xfers callback info ", logger.String("signature", signature), logger.String("req", string(reqByte)))

		var disburs xfers.DisbursementResponse
		err = json.Unmarshal(reqByte, &disburs)
		if err != nil {
			err = errors.SetErrorMessage(http.StatusInternalServerError, err.Error())
			logger.Error(ctx, err.Error())
			return
		}
		credential, _, errRes := c.getCredentialCashOutXfersCallback(ctx, disburs.Data.Attributes.ReferenceId)
		if errRes != nil {
			err = errors.SetErrorMessage(http.StatusInternalServerError, errRes.Error())
			logger.Error(ctx, err.Error())
			return
		}

		mac := utils.GenerateSignatureHmac256(string(reqByte), credential.SignKeySend)
		logger.Info(ctx, "generate signature inf0", logger.String("mac", mac), logger.String("sign key", credential.SignKeySend))
		if mac != signature {
			err = errors.SetErrorMessage(http.StatusBadRequest, "Signature not valid")
			logger.Error(ctx, err.Error())
			return
		}
		keyPublis = constants.CallbackCashOutPaymentXfers

	case constants.ProviderXendit:
		var disburs xendit.DisbursementResponse
		err = json.Unmarshal(reqByte, &disburs)
		if err != nil {
			err = errors.SetErrorMessage(http.StatusInternalServerError, err.Error())
			logger.Error(ctx, err.Error())
			return
		}

		credential, _, errRes := c.getCredentialCashOutXenditCallback(ctx, disburs.ExternalID)
		if errRes != nil {
			err = errors.SetErrorMessage(http.StatusInternalServerError, errRes.Error())
			logger.Error(ctx, err.Error())
			return
		}

		if credential.CallbackToken != signature {
			err = errors.SetErrorMessage(http.StatusUnauthorized, "token authorization not valid")
			logger.Error(ctx, err.Error())
			return
		}
		keyPublis = constants.CallbackCashoutPaymentXendit

	default:
		err = errors.SetErrorMessage(http.StatusBadRequest, "payment system not support provider")
		logger.Error(ctx, err.Error())
		return
	}

	msq := messagebrokerMessage.Message{
		Headers: logutil.SetAmqpTableFromCtx(ctx),
		Type:    messagebrokerConfig.CONTENT_TYPE_JSON,
		Content: reqByte,
	}
	infrastructure.PublishMessage(keyPublis, &msq)

	return
}

func (cu *defaultCashOutUsecase) ListHistory(ctx context.Context, param map[string]interface{}, orderParam map[string]interface{}) (res []interface{}, totalItem int64, err error) {
	res, totalItem, err = cu.cashOutRepository.ListHistory(ctx, param, orderParam)
	if err != nil {
		return
	}

	return
}

func (s *defaultCashOutUsecase) GetListItemCashInBatchNumber(ctx context.Context, req domain.GetCashoutItemParam) (resp *[]entity.ListItemCashInBatchNumber, total int64, err error) {
	resp, total, err = s.cashOutRepository.GetListItemCashInBatchNumber(ctx, req)
	if err != nil {
		err = errors.SetError(http.StatusInternalServerError, err.Error())
		logger.Error(ctx, err.Error())
		return
	}

	if resp == nil || len(*resp) == 0 {
		return
	}

	invoiceDone := make(map[string]bool)

	// Process the slice: show ProductFee only for the first occurrence of each invoice number
	// Since the slice is already sorted by invoice number, we can compare with the previous item
	for i := 0; i < len(*resp); i++ {
		// Parse and format settlement date
		if date, parseErr := utils.ParseUnixTime((*resp)[i].SettlementDate); parseErr == nil {
			(*resp)[i].SettlementDate = date
		}

		// replace product fee with company product fee
		(*resp)[i].ProductFee = (*resp)[i].CompanyProductFee

		// Zero out ProductFee for duplicate invoice number, use invoiceDone to flag if invoice number has been processed
		if _, exists := invoiceDone[(*resp)[i].InvoiceNumber]; exists {
			(*resp)[i].ProductFee = 0
		} else {
			invoiceDone[(*resp)[i].InvoiceNumber] = true
		}

	}

	return
}

func (s *defaultCashOutUsecase) Export(ctx context.Context, req *domain.CashoutExportReq) (filename string, err error) {
	//validate request
	dateNow := timepkg.DateNow()
	if req.StartDatetime.After(dateNow) || req.EndDatetime.After(dateNow) {
		err = errors.SetErrorMessage(http.StatusInternalServerError, "start date and end date cannot be greater than now")
		logger.Error(ctx, err.Error())
		return
	}

	lastYear := dateNow.AddDate(-1, 0, 0)
	if req.StartDatetime.Before(lastYear) {
		err = errors.SetErrorMessage(http.StatusInternalServerError, "max start date is last year")
		logger.Error(ctx, err.Error())
		return
	}

	cashoutInfo, err := s.cashOutRepository.GetByID(ctx, req.CashoutID)
	if err != nil {
		err = errors.SetErrorMessage(http.StatusNotFound, "cashout id is not found")
		logger.Error(ctx, err.Error())
		return
	}

	req.CashoutInfo = &cashoutInfo

	data, err := s.cashOutRepository.Export(ctx, req)
	if err != nil {
		err = errors.SetErrorMessage(http.StatusInternalServerError, "get data export error")
		logger.Error(ctx, err.Error())
		return
	}

	req.ShowFee = true
	excelFile, err := s.exportExcel(ctx, *req, data)
	if err != nil {
		err = errors.SetErrorMessage(http.StatusInternalServerError, fmt.Sprintf("error save excel %v", err))
		logger.Error(ctx, err.Error())
		return
	}

	filename = fmt.Sprintf("./static/%s_CashOut_%s_%s.xlsx", str.RandString(10), req.StartDatetime.Format("20060102"), req.EndDatetime.Format("20060102"))
	if err = excelFile.SaveAs(filename); err != nil {
		err = errors.SetErrorMessage(http.StatusInternalServerError, fmt.Sprintf("error save excel %v", err))
		logger.Error(ctx, err.Error())
		return
	}

	return
}

func (s *defaultCashOutUsecase) exportExcel(ctx context.Context, req domain.CashoutExportReq, data []domain.CashoutExportRes) (excelFile *excelize.File, err error) {
	excelFile = excelize.NewFile()
	defer func() {
		if err := excelFile.Close(); err != nil {
			fmt.Println("excel file err", err)
		}
	}()

	ac := accounting.Accounting{Symbol: "", Precision: 0, Thousand: "."}

	cashOutSheetName := "Cash Out Export"
	_ = excelFile.SetSheetName("Sheet1", cashOutSheetName)

	boldStyle, err := excelFile.NewStyle(&excelize.Style{
		Font: &excelize.Font{
			Bold: true,
		},
		Alignment: &excelize.Alignment{Horizontal: "center", Vertical: "center"},
	})
	if err != nil {
		err = errors.SetErrorMessage(http.StatusInternalServerError, "error create bold style")
		logger.Error(ctx, err.Error())
		return
	}

	boldAndBorderStyle1, err := excelFile.NewStyle(&excelize.Style{
		Font: &excelize.Font{
			Bold: true,
		},
		Alignment: &excelize.Alignment{Horizontal: "center", Vertical: "center"},
		Border: []excelize.Border{
			{Type: "top", Color: "#000000", Style: 1},
			{Type: "right", Color: "#000000", Style: 1},
			{Type: "left", Color: "#000000", Style: 1},
		},
	})
	boldAndBorderStyle2, err := excelFile.NewStyle(&excelize.Style{
		Font: &excelize.Font{
			Bold: true,
		},
		Alignment: &excelize.Alignment{Horizontal: "center", Vertical: "center"},
		Border: []excelize.Border{
			{Type: "right", Color: "#000000", Style: 1},
			{Type: "left", Color: "#000000", Style: 1},
			{Type: "bottom", Color: "#000000", Style: 1},
		},
	})
	if err != nil {
		err = errors.SetErrorMessage(http.StatusInternalServerError, "error create bold and border style")
		logger.Error(ctx, err.Error())
		return
	}

	// set header
	_ = excelFile.SetColWidth(cashOutSheetName, "A", "S", 25)

	_ = excelFile.SetCellValue(cashOutSheetName, "A1", "Rekap Cash Out report")
	_ = excelFile.SetCellStyle(cashOutSheetName, "A1", "A1", boldStyle)
	_ = excelFile.MergeCell(cashOutSheetName, "A1", "K1")

	_ = excelFile.SetCellValue(cashOutSheetName, "A2", fmt.Sprintf("Periode %s - %s", req.StartDatetime.Format("02/01/2006"), req.EndDatetime.Format("02/01/2006")))
	_ = excelFile.SetCellStyle(cashOutSheetName, "A2", "A2", boldStyle)
	_ = excelFile.MergeCell(cashOutSheetName, "A2", "K2")

	_ = excelFile.SetCellValue(cashOutSheetName, "A3", "Batch")
	_ = excelFile.SetCellStyle(cashOutSheetName, "A3", "A3", boldAndBorderStyle1)
	_ = excelFile.SetCellStyle(cashOutSheetName, "A4", "A4", boldAndBorderStyle2)
	_ = excelFile.MergeCell(cashOutSheetName, "A3", "A4")

	_ = excelFile.SetCellValue(cashOutSheetName, "B3", "Payment Number")
	_ = excelFile.SetCellStyle(cashOutSheetName, "B3", "B3", boldAndBorderStyle1)
	_ = excelFile.SetCellStyle(cashOutSheetName, "B4", "B4", boldAndBorderStyle2)
	_ = excelFile.MergeCell(cashOutSheetName, "B3", "B4")

	_ = excelFile.SetCellValue(cashOutSheetName, "C3", "No Invoice")
	_ = excelFile.SetCellStyle(cashOutSheetName, "C3", "C3", boldAndBorderStyle1)
	_ = excelFile.SetCellStyle(cashOutSheetName, "C4", "C4", boldAndBorderStyle2)
	_ = excelFile.MergeCell(cashOutSheetName, "C3", "C4")

	_ = excelFile.SetCellValue(cashOutSheetName, "D3", "Partner Name")
	_ = excelFile.SetCellStyle(cashOutSheetName, "D3", "D3", boldAndBorderStyle1)
	_ = excelFile.SetCellStyle(cashOutSheetName, "D4", "D4", boldAndBorderStyle2)
	_ = excelFile.MergeCell(cashOutSheetName, "D3", "D4")

	_ = excelFile.SetCellValue(cashOutSheetName, "E3", "Note")
	_ = excelFile.SetCellStyle(cashOutSheetName, "E3", "E3", boldAndBorderStyle1)
	_ = excelFile.SetCellStyle(cashOutSheetName, "E4", "E4", boldAndBorderStyle2)
	_ = excelFile.MergeCell(cashOutSheetName, "E3", "E4")

	_ = excelFile.SetCellValue(cashOutSheetName, "F3", "Customer Name")
	_ = excelFile.SetCellStyle(cashOutSheetName, "F3", "F3", boldAndBorderStyle1)
	_ = excelFile.SetCellStyle(cashOutSheetName, "F4", "F4", boldAndBorderStyle2)
	_ = excelFile.MergeCell(cashOutSheetName, "F3", "F4")

	_ = excelFile.SetCellValue(cashOutSheetName, "G3", "Customer Email")
	_ = excelFile.SetCellStyle(cashOutSheetName, "G3", "G3", boldAndBorderStyle1)
	_ = excelFile.SetCellStyle(cashOutSheetName, "G4", "G4", boldAndBorderStyle2)
	_ = excelFile.MergeCell(cashOutSheetName, "G3", "G4")

	_ = excelFile.SetCellValue(cashOutSheetName, "H3", "Settlement Date")
	_ = excelFile.SetCellStyle(cashOutSheetName, "H3", "H3", boldAndBorderStyle1)
	_ = excelFile.SetCellStyle(cashOutSheetName, "H4", "H4", boldAndBorderStyle2)
	_ = excelFile.MergeCell(cashOutSheetName, "H3", "H4")

	_ = excelFile.SetCellValue(cashOutSheetName, "I3", "Disbursement Date")
	_ = excelFile.SetCellStyle(cashOutSheetName, "I3", "I3", boldAndBorderStyle1)
	_ = excelFile.SetCellStyle(cashOutSheetName, "I4", "I4", boldAndBorderStyle2)
	_ = excelFile.MergeCell(cashOutSheetName, "I3", "I4")

	_ = excelFile.SetCellValue(cashOutSheetName, "J3", "Product Fee")
	_ = excelFile.SetCellStyle(cashOutSheetName, "J3", "J3", boldAndBorderStyle1)
	_ = excelFile.SetCellStyle(cashOutSheetName, "J4", "J4", boldAndBorderStyle2)
	_ = excelFile.MergeCell(cashOutSheetName, "J3", "J4")

	_ = excelFile.SetCellValue(cashOutSheetName, "K3", "Grand Total")
	_ = excelFile.SetCellStyle(cashOutSheetName, "K3", "K3", boldAndBorderStyle1)
	_ = excelFile.SetCellStyle(cashOutSheetName, "K4", "K4", boldAndBorderStyle2)
	_ = excelFile.MergeCell(cashOutSheetName, "K3", "K4")

	var subtotalItem float64
	var companyProductFeeTotal float64
	var totalDisbursement float64
	invoiceDone := make(map[string]bool)

	rowIndex := 5
	for _, d := range data {
		rowIndexS := strconv.Itoa(rowIndex)
		rowIndex++

		var (
			companyProductFee float64
			cashinNumber      string
			partnerName       string
			note              string
			customerName      string
			customerEmail     string
			settlementDate    string
		)

		if _, exists := invoiceDone[d.CashInInvoiceNumber]; !exists {
			invoiceDone[d.CashInInvoiceNumber] = true
			companyProductFee = d.CompanyProductFee
			cashinNumber = d.CashInInvoiceNumber
			partnerName = d.PartnerName
			note = d.CashInItemNote
			customerName = d.CustomerName
			customerEmail = d.CustomerEmail
			settlementDate = d.SettlementDate.String(timepkg.TimeDateExportFormat, timepkg.WIB)
		}

		_ = excelFile.SetCellValue(cashOutSheetName, "A"+rowIndexS, d.BatchNumber)
		_ = excelFile.SetCellValue(cashOutSheetName, "B"+rowIndexS, cashinNumber)
		_ = excelFile.SetCellValue(cashOutSheetName, "C"+rowIndexS, d.RefInvoiceNumber)
		_ = excelFile.SetCellValue(cashOutSheetName, "D"+rowIndexS, partnerName)
		_ = excelFile.SetCellValue(cashOutSheetName, "E"+rowIndexS, note)
		_ = excelFile.SetCellValue(cashOutSheetName, "F"+rowIndexS, customerName)
		_ = excelFile.SetCellValue(cashOutSheetName, "G"+rowIndexS, customerEmail)
		_ = excelFile.SetCellValue(cashOutSheetName, "H"+rowIndexS, settlementDate)
		_ = excelFile.SetCellValue(cashOutSheetName, "I"+rowIndexS, d.PaymentAt.String(timepkg.TimeDateExportFormat, timepkg.WIB))
		_ = excelFile.SetCellValue(cashOutSheetName, "J"+rowIndexS, ac.FormatMoney(companyProductFee))
		_ = excelFile.SetCellValue(cashOutSheetName, "K"+rowIndexS, ac.FormatMoney(d.GrandTotal))

		subtotalItem += d.GrandTotal
		companyProductFeeTotal += companyProductFee
	}

	moneyNumber, _ := excelFile.NewStyle(&excelize.Style{
		Alignment: &excelize.Alignment{Horizontal: "right", Vertical: "center"},
	})

	_ = excelFile.SetCellStyle(cashOutSheetName, "J5", "J"+strconv.Itoa(rowIndex-1), moneyNumber)
	_ = excelFile.SetCellStyle(cashOutSheetName, "K5", "K"+strconv.Itoa(rowIndex-1), moneyNumber)

	style, _ := excelFile.NewStyle(&excelize.Style{
		Font: &excelize.Font{
			Bold: true,
		},
		Alignment: &excelize.Alignment{Horizontal: "Right", Vertical: "center"},
	})

	style2, _ := excelFile.NewStyle(&excelize.Style{
		Font: &excelize.Font{
			Bold: true,
		},
		Alignment: &excelize.Alignment{Horizontal: "right", Vertical: "center"},
	})

	totalDisbursement = subtotalItem - companyProductFeeTotal

	startStyle := rowIndex
	_ = excelFile.SetCellValue(cashOutSheetName, "J"+strconv.Itoa(rowIndex), "Subtotal Item:")
	_ = excelFile.SetCellValue(cashOutSheetName, "K"+strconv.Itoa(rowIndex), ac.FormatMoney(subtotalItem))
	rowIndex++
	_ = excelFile.SetCellValue(cashOutSheetName, "J"+strconv.Itoa(rowIndex), "Product Fee:")
	_ = excelFile.SetCellValue(cashOutSheetName, "K"+strconv.Itoa(rowIndex), ac.FormatMoney(companyProductFeeTotal))
	rowIndex++
	endStyle := rowIndex
	_ = excelFile.SetCellValue(cashOutSheetName, "J"+strconv.Itoa(rowIndex), "Total Disbursement:")
	_ = excelFile.SetCellValue(cashOutSheetName, "K"+strconv.Itoa(rowIndex), ac.FormatMoney(totalDisbursement))

	_ = excelFile.SetCellStyle(cashOutSheetName, "J"+strconv.Itoa(startStyle), "J"+strconv.Itoa(endStyle), style)
	_ = excelFile.SetCellStyle(cashOutSheetName, "K"+strconv.Itoa(startStyle), "K"+strconv.Itoa(endStyle), style2)

	return
}

func (s *defaultCashOutUsecase) UpdateCashOutTransaction(ctx context.Context, cashOutId int, p *entity.ManualDoneRequest) (err error) {
	// validate if status is already done
	cashOut, err := s.cashOutRepository.GetByID(ctx, cashOutId)
	if err != nil {
		err = errors.SetErrorMessage(http.StatusInternalServerError, "get cash out transaction error")
		logger.Error(ctx, err.Error())
		return
	}

	if cashOut.Status == "done" {
		err = errors.SetErrorMessage(http.StatusAlreadyReported, "cash out transaction status already done")
		logger.Error(ctx, err.Error())
		return
	}

	locTime := utils.GetLocalTime()

	history := &domain.CashOutTransactionHistories{
		Description: fmt.Sprintf("manual done: %s", p.Reason),
		Status:      constants.CashOutStatusDone,
	}

	cashOut.PaymentAt = int(locTime.Unix())
	cashOut.Status = constants.CashOutStatusDone
	cashOut.CashOutTransactionHistories = history

	tx := s.cashInRepo.BeginTrans()
	defer tx.Rollback()

	_, err = s.cashOutRepository.Update(ctx, &cashOut, tx)
	if err != nil {
		logger.Error(ctx, err.Error())

		tx.Rollback()
		return err
	}

	err = s.cashOutRepository.UpdateCashOutDetails(ctx, cashOutId, tx)
	if err != nil {
		logger.Error(ctx, err.Error())

		tx.Rollback()
		return err
	}

	tx.Commit()

	return nil
}

func (s *defaultCashOutUsecase) ResendEmail(ctx context.Context, cashOutId int) (err error) {
	if err := s.SendCashoutEmail(ctx, cashOutId); err != nil {
		logger.Error(ctx, err.Error())
		return err
	}

	return nil
}
