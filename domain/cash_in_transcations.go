package domain

import (
	"context"
	"encoding/json"
	"net/http"
	"time"

	"gorm.io/gorm"
	"repo.nusatek.id/moaja/backend/libraries/logger"
	"repo.nusatek.id/moaja/backend/libraries/service-utils/errors"

	"repo.nusatek.id/nusatek/payment/utils/constants"
	"repo.nusatek.id/nusatek/payment/utils/interfacepkg"
	"repo.nusatek.id/nusatek/payment/utils/str"
)

// wrap calculation of cash in
type CashInCalculation struct {
	Total             float64 `json:"total" gorm:"column:total"` // for current already net price (include voucher and discount) (sudah dikurangi discount dan voucher)
	ProductFee        float64 `json:"product_fee" gorm:"column:product_fee"`
	CompanyProductFee float64 `json:"company_product_fee" gorm:"column:company_product_fee"`
	AdminFee          float64 `json:"admin_fee" gorm:"column:admin_fee"`
	Voucher           float64 `json:"voucher" gorm:"column:voucher"`
	Discount          float64 `json:"discount" gorm:"column:discount"`
	PgDeliveryFee     float64 `json:"pg_delivery_fee" gorm:"column:pg_delivery_fee"`
}

func (c CashInCalculation) GetGrandTotal() float64 {
	//c.Total already net price (include voucher and discount)
	return (c.Total + c.ProductFee)
}

func (c CashInCalculation) GetCashInTotal() float64 {
	return c.GetGrandTotal() - c.AdminFee
}

func (c CashInCalculation) GetSubTotalItem() float64 {
	// total all item - vocher
	return c.Total + c.Voucher
}

type CashInTransactions struct {
	ID                         int                         `gorm:"primaryKey" json:"id"`
	CompanyID                  int                         `json:"company_id"`
	CompanyCode                string                      `json:"company_code" gorm:"<-:false"`
	CompanyName                string                      `json:"company_name" gorm:"<-:false"`
	CompanyProductID           int                         `json:"company_product_id"`
	InvoiceNumber              string                      `gorm:"type:varchar(64); not null" json:"invoice_number" validate:"required"`
	PaymentAt                  int                         `gorm:"type:bigint; not null" json:"payment_at"`
	ManualPaymentAt            *int                        `gorm:"type:bigint" json:"manual_payment_at"` // trigger when user use manual paid
	CustomerName               string                      `gorm:"type:varchar(64); not null" json:"customer_name" validate:"required"`
	CustomerPhone              string                      `gorm:"type:varchar(64); not null" json:"customer_phone" validate:"min=10"`
	CustomerEmail              string                      `gorm:"type:varchar(64); not null" json:"customer_email" validate:"email"`
	PaymentProviderID          int                         `json:"payment_provider_id"`
	PaymentProviderName        string                      `json:"payment_provider_name" gorm:"<-:false"`
	PaymentChannelID           int                         `json:"payment_channel_id"`
	PaymentChannelType         string                      `json:"payment_channel_type" gorm:"<-:false"`
	Total                      float64                     `json:"total"` // for current already net price (include voucher and discount) (sudah dikurangi discount dan voucher)
	AdminFee                   float64                     `json:"admin_fee"`
	Discount                   float64                     `json:"discount"`
	Voucher                    float64                     `json:"voucher"`
	PgDeliveryFee              float64                     `json:"pg_delivery_fee"`
	PaymentStatus              string                      `json:"payment_status"`
	Status                     string                      `json:"status"`
	ProductFee                 float64                     `json:"product_fee"`         // transaction fee
	CompanyProductFee          float64                     `json:"company_product_fee"` // product_fee
	PgReferenceInformation     string                      `json:"pg_reference_information" gorm:"<-:false"`
	ExpiredAt                  *time.Time                  `json:"expired_at"`
	CreatedAt                  time.Time                   `json:"created_at"`
	UpdatedAt                  time.Time                   `json:"updated_at"`
	DeletedAt                  gorm.DeletedAt              `gorm:"index" json:"deleted_at"`
	CashInTransactionItems     *[]CashInTransactionItems   `gorm:"foreignKey:CashInTransactionID;constraint:OnUpdate:CASCADE,OnDelete:CASCADE;" json:"items,omitempty"`
	CashInTranscationHistories *CashInTransactionHistories `gorm:"foreignKey:CashInTransactionID;constraint:OnUpdate:CASCADE,OnDelete:CASCADE;" json:"history,omitempty"`
	AdditionalInfo             CashInAdditionalInfo        `json:"-" gorm:"-"`
}

func (c *CashInTransactions) ManualPayment(ctx context.Context, paymentAt time.Time) (err error) {
	if !str.Contains([]string{constants.CashInStatusPending, constants.CashInStatusExpired}, c.Status) {
		err = errors.SetError(http.StatusBadRequest, "invalid status for manual payment")
		logger.Error(ctx, err.Error())
		return
	}

	manualPaymentAt := int(time.Now().Unix())
	c.PaymentAt = int(paymentAt.Unix())
	c.ManualPaymentAt = &manualPaymentAt

	return nil
}

func (c CashInTransactions) IsExpired() bool {
	isExpiredTime := false
	if c.ExpiredAt != nil {
		isExpiredTime = !time.Now().Before(*c.ExpiredAt)
	}
	return c.PaymentStatus == constants.PaymentExpired || isExpiredTime
}

type CashInAdditionalInfo map[string]interface{}

func (c CashInAdditionalInfo) ToVA() (res CashInAdditionalInfoVA) {
	_ = interfacepkg.Convert(c, &res)
	return
}

func (c CashInAdditionalInfo) ToCashlez() (res CashInAdditionalInfoCashlezz) {
	_ = interfacepkg.Convert(c, &res)
	return
}

type CashInAdditionalInfoVA struct {
	IsAllowedRandomVA      bool `json:"is_allowed_random_va"`
	IsOverwriteTransaction bool `json:"is_overwrite_transaction"` // make previous transaction to failed and create new transaction
}

type CashInAdditionalInfoCashlezz struct {
	Cashlez CashInAdditionalInfoCashlezzData `json:"cashlez"`
}
type CashInAdditionalInfoCashlezzData struct {
	MerchantName          string `json:"merchant_name"`
	MerchantTransactionID string `json:"merchant_transaction_id"`
	Amount                string `json:"amount"`
	TransactionType       string `json:"transaction_type"`
	PaymentName           string `json:"payment_name"`
	Email                 string `json:"email"`
	NoHandphone           string `json:"no_handphone"`
	Autobackpref          bool   `json:"autobackpref"`
	AutoPrint             bool   `json:"auto_print"`
	AutoRedirect          bool   `json:"auto_redirect"`
	RedirectTime          string `json:"redirect_time"`
}

type CashinTransactionCallBack struct {
	CashInTransactions
	ProductCode string `json:"product_code"`
	CompanyCode string `json:"company_code"`
}
type CashInTransactionDetail struct {
	ID                            int       `json:"id" gorm:"column:id;<-false"`
	CompanyID                     int       `json:"company_id" gorm:"column:company_id;<-false"`
	CompanyName                   string    `json:"company_name" gorm:"column:company_name;<-false"`
	ProductName                   string    `json:"product_name" gorm:"column:product_name;<-false"`
	CompanyProductID              int       `json:"company_product_id" gorm:"column:company_product_id;<-false"`
	InvoiceNumber                 string    `json:"invoice_number" gorm:"column:invoice_number;<-false"`
	PaymentAt                     UnixTime  `json:"payment_at" gorm:"column:payment_at;<-false"`
	ManualPaymentAt               *UnixTime `json:"manual_payment_at" gorm:"column:manual_payment_at;<-false"`
	CustomerName                  string    `json:"customer_name" gorm:"column:customer_name;<-false"`
	CustomerPhone                 string    `json:"customer_phone" gorm:"column:customer_phone;<-false"`
	CustomerEmail                 string    `json:"customer_email" gorm:"column:customer_email;<-false"`
	PaymentProviderID             int       `json:"payment_provider_id" gorm:"column:payment_provider_id;<-false"`
	PaymentChannelID              int       `json:"payment_channel_id" gorm:"column:payment_channel_id;<-false"`
	PaymentStatus                 string    `json:"payment_status" gorm:"column:payment_status;<-false"`
	PgDeliveryFee                 float64   `json:"delivery_fee" gorm:"column:delivery_fee;<-false"`
	Status                        string    `json:"status" gorm:"column:status;<-false"`
	CreatedAt                     time.Time `json:"created_at" gorm:"column:created_at;<-false"`
	UpdatedAt                     time.Time `json:"updated_at" gorm:"column:updated_at;<-false"`
	ExpiredAt                     time.Time `json:"expired_at" gorm:"column:expired_at;<-false"`
	PaymentProviderName           string    `json:"payment_provider_name" gorm:"column:payment_provider_name;<-false"`
	PaymentChannelName            string    `json:"payment_channel_name" gorm:"column:payment_channel_name;<-false"`
	PaymentChannelTypePaymentType string    `json:"payment_channel_type_payment_type" gorm:"column:payment_channel_type_payment_type;<-false"`
	CashInCalculation
	CompanyProductCashoutFeeFixValue   float64 `json:"company_product_cashout_fee_fix_value" gorm:"column:company_product_cashout_fee_fix_value"`
	CompanyProductCashoutFeePercentage float64 `json:"company_product_cashout_fee_percentage" gorm:"column:company_product_cashout_fee_percentage"`
}

type CashInTransactionWithItemCount struct {
	ID                int                  `gorm:"column:id;<-false"`
	InvoiceNumber     string               `gorm:"column:invoice_number;<-false"`
	Status            string               `gorm:"column:status;<-false"`
	PaymentStatus     string               `gorm:"column:payment_status;<-false"`
	PaymentProviderID int                  `gorm:"column:payment_provider_id;<-false"`
	PaymentChannelID  int                  `gorm:"column:payment_channel_id;<-false"`
	ItemCount         int                  `gorm:"column:item_count;<-false"`
	ItemStatuses      StringCommaSeparated `gorm:"column:item_statuses;<-false"`
}

type CashinExportReq struct {
	DateFilterType    string // created, settlement
	StartDatetime     time.Time
	EndDatetime       time.Time
	Statuses          []string
	CompanyID         int
	PaymentProviderID []int
	PaymentChannelID  []int
	PartnerID         []int
}

type CashinExportRes struct {
	InvoiceNumber       string                   `gorm:"column:invoice_number;<-false"`
	CustomerName        string                   `gorm:"column:customer_name;<-false"`
	CompanyProductCode  string                   `gorm:"column:company_product_code;<-false"`
	PaymentProviderName string                   `gorm:"column:payment_provider_name;<-false"`
	PaymentChannelName  string                   `gorm:"column:payment_channel_name;<-false"`
	PaymentAt           UnixTime                 `gorm:"column:payment_at;<-false"`
	CreatedAt           time.Time                `gorm:"column:created_at;<-false"`
	Status              string                   `gorm:"column:status;<-false"`
	VirtualAccount      string                   `gorm:"column:virtual_account;<-false"`
	Items               CashinExportItemsJSONRes `gorm:"column:items;<-false"`
	CashInCalculation
	CompanyProductCashoutFeeFixValue   float64 `json:"company_product_cashout_fee_fix_value" gorm:"column:company_product_cashout_fee_fix_value"`
	CompanyProductCashoutFeePercentage float64 `json:"company_product_cashout_fee_percentage" gorm:"column:company_product_cashout_fee_percentage"`
}

type CashinExportItemsJSONRes json.RawMessage

func (c CashinExportItemsJSONRes) Value() (items []CashinExportItemRes) {
	_ = json.Unmarshal(c, &items)
	return
}

type CashinExportItemRes struct {
	RefInvoiceNumber string  `json:"ref_invoice_number"`
	Amount           float64 `json:"amount"`
	Note             string  `json:"note"`
	Status           string  `json:"status"`
	PartnerName      string  `json:"partner_name"`
}

type UpdateWachingStatusCashinOpts struct {
	HistoryDescription string
	PaymentAt          *time.Time
}

// used for update status in callback

type ToSnapCbOpts struct {
	CustomerNumber      string
	PaymentProviderName string
	PartnerId           string
}

type SnapCbCashInTransaction struct {
	ID                  int         `json:"id"`
	InvoiceNumber       string      `json:"invoice_number"`
	CustomerNumber      string      `json:"customer_number"`
	Status              string      `json:"status"`
	ExpiredAt           *time.Time  `json:"expired_at"`
	CreatedAt           time.Time   `json:"created_at"`
	UpdatedAt           time.Time   `json:"updated_at"`
	PaymentProviderName string      `json:"payment_provider_name"` // payment provider name
	PartnerId           string      `json:"partner_id"`
	Request             interface{} `json:"request"`
	Response            interface{} `json:"response"`
}

func (c CashInTransactions) ToSnapCb(opts ToSnapCbOpts) SnapCbCashInTransaction {
	return SnapCbCashInTransaction{
		ID:                  c.ID,
		InvoiceNumber:       c.InvoiceNumber,
		CustomerNumber:      opts.CustomerNumber,
		Status:              c.Status,
		ExpiredAt:           c.ExpiredAt,
		CreatedAt:           c.CreatedAt,
		UpdatedAt:           c.UpdatedAt,
		PaymentProviderName: opts.PaymentProviderName,
		PartnerId:           opts.PartnerId,
	}
}

func (s *SnapCbCashInTransaction) ToStatus(to string) SnapCbCashInTransaction {
	s.Status = to
	return *s
}

const (
	CashoutCallbackCreated = "CREATED"
	CashoutCallbackUpdated = "UPDATED"
)

type CashOutCallback struct {
	Operation    string                        `json:"operation"`
	CompanyCode  string                        `json:"company_code"`
	ProductName  string                        `json:"product_name"`
	ProductCode  string                        `json:"product_code"`
	PartnerName  string                        `json:"partner_name"`
	PartnerCode  string                        `json:"partner_code"`
	BatchNumber  string                        `json:"batch_number"`
	Status       string                        `json:"status"`
	CashoutItems []CashOutCallbackCashoutItems `json:"cashout_items"`
}
type CashOutCallbackCashin struct {
	InvoiceNumber string `json:"invoice_number"`
	Status        string `json:"status"`
	PaymentStatus string `json:"payment_status"`
}
type CashOutCallbackCashoutItems struct {
	CashIn           CashOutCallbackCashin `json:"cash_in"`
	RefInvoiceNumber string                `json:"ref_invoice_number"`
	ItemName         string                `json:"item_name"`
	Status           string                `json:"status"`
}
