package domain

import (
	"time"

	"gorm.io/gorm"

	"repo.nusatek.id/nusatek/payment/utils"
)

type CashInTransactionItems struct {
	ID                  int            `json:"id"`
	CashInTransactionID int            `json:"cashin_transaction_id"`
	RefInvoiceNumber    string         `json:"ref_invoice_number"`
	PartnerId           int            `json:"partner_id"`
	ItemName            string         `json:"item_name"`
	Amount              float64        `json:"amount"`
	Status              string         `json:"status"`
	Note                string         `json:"note"`
	IsCashout           bool           `json:"is_cashout"`
	SlaDate             *time.Time     `json:"sla_date"`
	CreatedAt           time.Time      `json:"created_at"`
	UpdatedAt           time.Time      `json:"updated_at"`
	DeletedAt           gorm.DeletedAt `gorm:"index" json:"deleted_at"`
}
type CashInTransactionItemsWithCashIn struct {
	CashInTransactionItems
	CashInInvoiceNumber string `gorm:"column:cash_in_invoice_number;<-:false"`
}

type CashInTransactionItemsCashout struct {
	CashInTransactionItems
	CashInInvoiceNumber               string `gorm:"column:cash_in_invoice_number;<-:false"`
	CashInTransactionCompanyProductID int    `gorm:"column:cash_in_transaction_company_product_id;<-:false"`
}

func (CashInTransactionItemsCashout) Table() string {
	return "cash_in_transaction_items"
}

// complete item
type CashInTransactionItemComplete struct {
	ID                  int        `json:"id" gorm:"column:id;<-false"`
	CashInTransactionID int        `json:"cash_in_transaction_id" gorm:"column:cash_in_transaction_id;<-false"`
	PartnerId           int        `json:"partner_id" gorm:"column:partner_id;<-false"`
	PartnerName         string     `json:"partner_name" gorm:"column:partner_name;<-false"`
	RefInvoiceNumber    string     `json:"ref_invoice_number" gorm:"column:ref_invoice_number;<-false"`
	ItemName            string     `json:"item_name" gorm:"column:item_name;<-false"`
	Amount              float64    `json:"amount" gorm:"column:amount;<-false"`
	Status              string     `json:"status" gorm:"column:status;<-false"`
	Note                string     `json:"note" gorm:"column:note;<-false"`
	IsCashout           bool       `json:"is_cashout" gorm:"column:is_cashout;<-false"`
	SlaDate             *time.Time `json:"sla_date" gorm:"column:sla_date;<-false"`
	CreatedAt           time.Time  `json:"created_at" gorm:"column:created_at;<-false"`
	UpdatedAt           time.Time  `json:"updated_at" gorm:"column:updated_at;<-false"`
	ReconciledDate      *time.Time `json:"reconciled_date" gorm:"column:reconciled_date;<-false"`
	DisbursementDate    UnixTime   `json:"disbursement_date" gorm:"column:disbursement_date;<-false"`
	DisbursementStatus  string     `json:"disbursement_status" gorm:"column:disbursement_status;<-false"`
	IsOutstanding       bool       `json:"is_outstanding" gorm:"column:is_outstanding;<-false"`
	BatchNumberCashOut  string     `json:"batch_number_cash_out" gorm:"column:batch_number_cash_out;<-false"`
}

type GetCashoutItemParam struct {
	CashoutId int
	Paginate  utils.Pagination
	DateType  string
	FromDate  time.Time
	ToDate    time.Time
	WithTotal bool
}

type CashInTransactionItemsOutstandingParam struct {
	PartnerID           int
	CompanyProductId    int
	CashInItemIDs       []int
	Search              string
	Paginate            utils.Pagination
	SettlementDateStart time.Time
	SettlementDateEnd   time.Time
	WithTotal           bool
}

type CashInTransactionItemsOutstanding struct {
	InvoiceNumber                     string     `gorm:"column:invoice_number;<-:false" json:"invoice_number"`
	PaymentChannelName                string     `gorm:"column:payment_channel_name;<-:false" json:"payment_channel_name"`
	PaymentProviderName               string     `gorm:"column:payment_provider_name;<-:false" json:"payment_provider_name"`
	PaymentAt                         int        `gorm:"column:payment_at;<-:false" json:"payment_at"`
	ID                                int        `gorm:"column:id" json:"id"`
	CashInTransactionID               int        `gorm:"column:cash_in_transaction_id" json:"cash_in_transaction_id"`
	CashInTransactionCompanyProductID int        `gorm:"column:cash_in_transaction_company_product_id" json:"cash_in_transaction_company_product_id"`
	CustomerName                      string     `gorm:"column:customer_name;<-:false" json:"customer_name"`
	TotalAmount                       float64    `gorm:"column:total;<-:false" json:"total_amount"`
	PartnerId                         int        `gorm:"column:partner_id" json:"partner_id"`
	PartnerName                       string     `gorm:"column:partner_name;<-:false" json:"partner_name"`
	ItemName                          string     `gorm:"column:item_name" json:"item_name"`
	Amount                            float64    `gorm:"column:amount" json:"amount"`
	Status                            string     `gorm:"column:status" json:"status"`
	IsCashout                         bool       `gorm:"column:is_cashout" json:"is_cashout"`
	SlaDate                           *time.Time `gorm:"column:sla_date" json:"sla_date"`
	CreatedAt                         time.Time  `gorm:"column:created_at" json:"created_at"`
	UpdatedAt                         time.Time  `gorm:"column:updated_at" json:"updated_at"`
}
